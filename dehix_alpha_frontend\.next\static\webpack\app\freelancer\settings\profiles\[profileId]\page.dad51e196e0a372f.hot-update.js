"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/[profileId]/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/[profileId]/page.tsx":
/*!*******************************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/[profileId]/page.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfileDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_cards_freelancerProjectCard__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/cards/freelancerProjectCard */ \"(app-pages-browser)/./src/components/cards/freelancerProjectCard.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_dialogs_ProjectSelectionDialog__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/dialogs/ProjectSelectionDialog */ \"(app-pages-browser)/./src/components/dialogs/ProjectSelectionDialog.tsx\");\n/* harmony import */ var _components_dialogs_ExperienceSelectionDialog__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/dialogs/ExperienceSelectionDialog */ \"(app-pages-browser)/./src/components/dialogs/ExperienceSelectionDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProfileDetailPage() {\n    var _selectedProject_techUsed;\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_20__.useSelector)((state)=>state.user);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const profileId = params.profileId;\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProfileData, setEditingProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [skillsOptions, setSkillsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [domainsOptions, setDomainsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [skillsAndDomainsLoaded, setSkillsAndDomainsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProjectDialog, setShowProjectDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExperienceDialog, setShowExperienceDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [freelancerProjects, setFreelancerProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProjectDetailsOpen, setIsProjectDetailsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tmpSkill, setTmpSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tmpDomain, setTmpDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (profileId) {\n            fetchSkillsAndDomains().then(()=>{\n                fetchProfile();\n                fetchFreelancerProjects();\n            });\n        }\n    }, [\n        profileId,\n        user.uid\n    ]);\n    // Helper function to get skill name from ID\n    const getSkillNameById = (skillId)=>{\n        if (!skillId || !skillsOptions || skillsOptions.length === 0) {\n            return skillId || \"\";\n        }\n        // Try multiple matching strategies\n        let skill = skillsOptions.find((s)=>s.skillId === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s._id === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s.id === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s.name === skillId);\n        // If still not found, try case-insensitive name matching\n        if (!skill) {\n            skill = skillsOptions.find((s)=>{\n                var _this;\n                return ((_this = s.name || s.label || s.skillName) === null || _this === void 0 ? void 0 : _this.toLowerCase()) === skillId.toLowerCase();\n            });\n        }\n        return skill ? skill.name || skill.label || skill.skillName || skillId : skillId;\n    };\n    // Helper function to get domain name from ID\n    const getDomainNameById = (domainId)=>{\n        if (!domainId || !domainsOptions || domainsOptions.length === 0) {\n            return domainId || \"\";\n        }\n        // Try multiple matching strategies\n        let domain = domainsOptions.find((d)=>d.domainId === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d._id === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d.id === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d.name === domainId);\n        // If still not found, try case-insensitive name matching\n        if (!domain) {\n            domain = domainsOptions.find((d)=>{\n                var _this;\n                return ((_this = d.name || d.label || d.domainName) === null || _this === void 0 ? void 0 : _this.toLowerCase()) === domainId.toLowerCase();\n            });\n        }\n        return domain ? domain.name || domain.label || domain.domainName || domainId : domainId;\n    };\n    // Helper function to transform profile data for backend API\n    const transformProfileForAPI = (profileData)=>{\n        var _profileData_skills, _profileData_domains;\n        const transformedSkills = ((_profileData_skills = profileData.skills) === null || _profileData_skills === void 0 ? void 0 : _profileData_skills.map((skill)=>{\n            if (typeof skill === \"string\") {\n                return skill;\n            }\n            // Prioritize skillId field, then fallback to other ID fields\n            const skillId = skill.skillId || skill._id || skill.id || skill.value || skill.name;\n            return skillId;\n        })) || [];\n        const transformedDomains = ((_profileData_domains = profileData.domains) === null || _profileData_domains === void 0 ? void 0 : _profileData_domains.map((domain)=>{\n            if (typeof domain === \"string\") {\n                return domain;\n            }\n            // Prioritize domainId field, then fallback to other ID fields\n            const domainId = domain.domainId || domain._id || domain.id || domain.value || domain.name;\n            return domainId;\n        })) || [];\n        return {\n            ...profileData,\n            skills: transformedSkills,\n            domains: transformedDomains\n        };\n    };\n    const fetchProfile = async ()=>{\n        if (!profileId) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/profile/\".concat(profileId));\n            const profileData = response.data.data;\n            // If profile has projects, fetch complete project data to ensure we have thumbnails\n            if (profileData.projects && profileData.projects.length > 0) {\n                try {\n                    const freelancerResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid));\n                    const freelancerData = freelancerResponse.data.data || {};\n                    const freelancerProjects = freelancerData.projects || {};\n                    // Convert projects object to array if it's an object\n                    const allFreelancerProjects = Array.isArray(freelancerProjects) ? freelancerProjects : Object.values(freelancerProjects);\n                    // Merge profile projects with complete freelancer project data\n                    const enrichedProjects = profileData.projects.map((profileProject)=>{\n                        const fullProject = allFreelancerProjects.find((fp)=>fp._id === profileProject._id);\n                        // Use full project data if available, otherwise use profile project data\n                        return fullProject || profileProject;\n                    });\n                    profileData.projects = enrichedProjects;\n                } catch (projectError) {\n                    console.warn(\"Could not fetch complete project data:\", projectError);\n                // Continue with existing profile data if project fetch fails\n                }\n            }\n            // Ensure skills and domains are properly formatted as arrays of strings\n            const processedProfileData = {\n                ...profileData,\n                skills: Array.isArray(profileData.skills) ? profileData.skills : [],\n                domains: Array.isArray(profileData.domains) ? profileData.domains : []\n            };\n            setProfile(processedProfileData);\n            setEditingProfileData(processedProfileData);\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profile\",\n                variant: \"destructive\"\n            });\n            router.push(\"/freelancer/settings/profiles\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const fetchSkillsAndDomains = async ()=>{\n        try {\n            const freelancerResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid));\n            const freelancerData = freelancerResponse.data.data || {};\n            const skillsData = freelancerData.skills || [];\n            const skillsArray = Array.isArray(skillsData) ? skillsData : [];\n            setSkillsOptions(skillsArray);\n            const domainsData = freelancerData.domain || [];\n            const domainsArray = Array.isArray(domainsData) ? domainsData : [];\n            setDomainsOptions(domainsArray);\n            setSkillsAndDomainsLoaded(true);\n        } catch (error) {\n            console.error(\"Error fetching skills and domains:\", error);\n        }\n    };\n    const fetchFreelancerProjects = async ()=>{\n        try {\n            const freelancerResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid));\n            const freelancerData = freelancerResponse.data.data || {};\n            console.log(\"Kapil Kapil Kapil Kapil Kapil Kapil Kapil Kapil Kapil Kapil Kapil Kapil Kapil Kapil Kapil \", freelancerData);\n            const projectsData = freelancerData.projects || {};\n            setFreelancerProjects(projectsData);\n        } catch (error) {\n            console.error(\"Error fetching freelancer projects:\", error);\n        }\n    };\n    const handleUpdateProfile = async ()=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        // Client-side validation\n        if (!editingProfileData.profileName || editingProfileData.profileName.trim().length === 0) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Profile name is required\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!editingProfileData.description || editingProfileData.description.trim().length < 10) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Description must be at least 10 characters long\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (editingProfileData.profileName.length > 100) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Profile name must be less than 100 characters\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (editingProfileData.description.length > 500) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Description must be less than 500 characters\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsUpdating(true);\n        try {\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI(editingProfileData);\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Profile updated successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setEditingProfileData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleAddSkill = ()=>{\n        if (!tmpSkill || !profile || !skillsOptions || skillsOptions.length === 0) return;\n        const selectedSkill = skillsOptions.find((skill)=>(skill.name || skill.label || skill.skillName) === tmpSkill);\n        if (selectedSkill) {\n            var _profile_skills;\n            // Add the skillId (string) to the profile, not the entire object\n            const skillIdToAdd = selectedSkill.skillId || selectedSkill._id || selectedSkill.id || selectedSkill.name;\n            // Check if skill is already added by comparing IDs\n            const isAlreadyAdded = (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((skill)=>{\n                const existingSkillId = typeof skill === \"string\" ? skill : skill.skillId || skill._id || skill.id || skill.name;\n                return existingSkillId === skillIdToAdd;\n            });\n            if (!isAlreadyAdded) {\n                const updatedSkills = [\n                    ...profile.skills || [],\n                    skillIdToAdd\n                ];\n                setProfile({\n                    ...profile,\n                    skills: updatedSkills\n                });\n                setEditingProfileData({\n                    ...editingProfileData,\n                    skills: updatedSkills\n                });\n            }\n            setTmpSkill(\"\");\n            setSearchQuery(\"\");\n        }\n    };\n    const handleAddDomain = ()=>{\n        if (!tmpDomain || !profile || !domainsOptions || domainsOptions.length === 0) return;\n        const selectedDomain = domainsOptions.find((domain)=>(domain.name || domain.label || domain.domainName) === tmpDomain);\n        if (selectedDomain) {\n            var _profile_domains;\n            // Add the domainId (string) to the profile, not the entire object\n            const domainIdToAdd = selectedDomain.domainId || selectedDomain._id || selectedDomain.id || selectedDomain.name;\n            // Check if domain is already added by comparing IDs\n            const isAlreadyAdded = (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((domain)=>{\n                const existingDomainId = typeof domain === \"string\" ? domain : domain.domainId || domain._id || domain.id || domain.name;\n                return existingDomainId === domainIdToAdd;\n            });\n            if (!isAlreadyAdded) {\n                const updatedDomains = [\n                    ...profile.domains || [],\n                    domainIdToAdd\n                ];\n                setProfile({\n                    ...profile,\n                    domains: updatedDomains\n                });\n                setEditingProfileData({\n                    ...editingProfileData,\n                    domains: updatedDomains\n                });\n            }\n            setTmpDomain(\"\");\n            setSearchQuery(\"\");\n        }\n    };\n    const handleDeleteSkill = (skillIdToDelete)=>{\n        if (!profile || !profile.skills) return;\n        const updatedSkills = profile.skills.filter((skill)=>{\n            const skillId = typeof skill === \"string\" ? skill : skill.skillId || skill._id || skill.id || skill.name;\n            return skillId !== skillIdToDelete;\n        });\n        setProfile({\n            ...profile,\n            skills: updatedSkills\n        });\n        setEditingProfileData({\n            ...editingProfileData,\n            skills: updatedSkills\n        });\n    };\n    const handleDeleteDomain = (domainIdToDelete)=>{\n        if (!profile || !profile.domains) return;\n        const updatedDomains = profile.domains.filter((domain)=>{\n            const domainId = typeof domain === \"string\" ? domain : domain.domainId || domain._id || domain.id || domain.name;\n            return domainId !== domainIdToDelete;\n        });\n        setProfile({\n            ...profile,\n            domains: updatedDomains\n        });\n        setEditingProfileData({\n            ...editingProfileData,\n            domains: updatedDomains\n        });\n    };\n    const handleRemoveProject = async (projectId)=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            const updatedProjects = (profile.projects || []).filter((project)=>project._id !== projectId);\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI({\n                ...profile,\n                projects: updatedProjects\n            });\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Project removed from profile successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error removing project:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to remove project from profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleRemoveExperience = async (experienceId)=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            const updatedExperiences = (profile.experiences || []).filter((experience)=>experience._id !== experienceId);\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI({\n                ...profile,\n                experiences: updatedExperiences\n            });\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Experience removed from profile successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error removing experience:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to remove experience from profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleProjectClick = (project)=>{\n        setSelectedProject(project);\n        setIsProjectDetailsOpen(true);\n    };\n    const handleCloseProjectDetails = ()=>{\n        setIsProjectDetailsOpen(false);\n        setSelectedProject(null);\n    };\n    const handleDeleteProfile = async ()=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.delete(\"/freelancer/profile/\".concat(profile._id));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            router.push(\"/freelancer/settings/profiles\");\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n        }\n    };\n    if (isLoading || !skillsAndDomainsLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                    menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                    active: \"Profiles\",\n                    isKycCheck: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 558,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                            menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                            activeMenu: \"Profiles\",\n                            breadcrumbItems: [\n                                {\n                                    label: \"Freelancer\",\n                                    link: \"/dashboard/freelancer\"\n                                },\n                                {\n                                    label: \"Settings\",\n                                    link: \"#\"\n                                },\n                                {\n                                    label: \"Profiles\",\n                                    link: \"/freelancer/settings/profiles\"\n                                },\n                                {\n                                    label: \"Profile Details\",\n                                    link: \"#\"\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading profile...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 564,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n            lineNumber: 557,\n            columnNumber: 7\n        }, this);\n    }\n    if (!profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                    menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                    active: \"Profiles\",\n                    isKycCheck: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 589,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                            menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                            activeMenu: \"Profiles\",\n                            breadcrumbItems: [\n                                {\n                                    label: \"Freelancer\",\n                                    link: \"/dashboard/freelancer\"\n                                },\n                                {\n                                    label: \"Settings\",\n                                    link: \"#\"\n                                },\n                                {\n                                    label: \"Profiles\",\n                                    link: \"/freelancer/settings/profiles\"\n                                },\n                                {\n                                    label: \"Profile Details\",\n                                    link: \"#\"\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 596,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Profile not found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: ()=>router.push(\"/freelancer/settings/profiles\"),\n                                        className: \"mt-4\",\n                                        children: \"Back to Profiles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 595,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n            lineNumber: 588,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 625,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"/freelancer/settings/profiles\"\n                            },\n                            {\n                                label: profile.profileName,\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/freelancer/settings/profiles\"),\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Profiles\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: profile.profileName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Edit your professional profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    onClick: handleUpdateProfile,\n                                                    disabled: isUpdating,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isUpdating ? \"Updating...\" : \"Save Changes\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    variant: \"destructive\",\n                                                    onClick: ()=>setDeleteDialogOpen(true),\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Delete\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Profile Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                            htmlFor: \"profileName\",\n                                                                            children: \"Profile Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 696,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm \".concat((editingProfileData.profileName || \"\").length === 0 ? \"text-red-500\" : (editingProfileData.profileName || \"\").length > 100 ? \"text-red-500\" : \"text-muted-foreground\"),\n                                                                            children: [\n                                                                                (editingProfileData.profileName || \"\").length,\n                                                                                \"/100\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"profileName\",\n                                                                    value: editingProfileData.profileName || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"profileName\", e.target.value),\n                                                                    placeholder: \"e.g., Frontend Developer\",\n                                                                    className: (editingProfileData.profileName || \"\").length === 0 || (editingProfileData.profileName || \"\").length > 100 ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"hourlyRate\",\n                                                                    children: \"Hourly Rate ($)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"hourlyRate\",\n                                                                    type: \"number\",\n                                                                    value: editingProfileData.hourlyRate || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"hourlyRate\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"description\",\n                                                                    children: \"Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat((editingProfileData.description || \"\").length < 10 ? \"text-red-500\" : (editingProfileData.description || \"\").length > 500 ? \"text-red-500\" : \"text-muted-foreground\"),\n                                                                    children: [\n                                                                        (editingProfileData.description || \"\").length,\n                                                                        \"/500 (min: 10)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                            id: \"description\",\n                                                            value: editingProfileData.description || \"\",\n                                                            onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                            placeholder: \"Describe your expertise and experience... (minimum 10 characters)\",\n                                                            rows: 4,\n                                                            className: (editingProfileData.description || \"\").length < 10 || (editingProfileData.description || \"\").length > 500 ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    children: \"Skills\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                            onValueChange: (value)=>{\n                                                                                setTmpSkill(value);\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            value: tmpSkill || \"\",\n                                                                            onOpenChange: (open)=>{\n                                                                                if (!open) setSearchQuery(\"\");\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                        placeholder: tmpSkill ? tmpSkill : \"Select skill\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                        lineNumber: 794,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 793,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"text\",\n                                                                                                    value: searchQuery,\n                                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                                    placeholder: \"Search skills\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 801,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 809,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 800,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        skillsOptions && skillsOptions.length > 0 && skillsOptions.filter((skill)=>{\n                                                                                            var _profile_skills;\n                                                                                            const skillName = skill.name || skill.label || skill.skillName;\n                                                                                            if (!skillName) return false;\n                                                                                            const matchesSearch = skillName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((s)=>{\n                                                                                                const existingSkillId = typeof s === \"string\" ? s : s.skillId || s._id || s.id || s.name;\n                                                                                                const currentSkillId = skill.skillId || skill._id || skill.id || skill.name;\n                                                                                                return existingSkillId === currentSkillId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                value: skill.name || skill.label || skill.skillName,\n                                                                                                children: skill.name || skill.label || skill.skillName\n                                                                                            }, skill.skillId || skill._id || skill.id || index, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                lineNumber: 848,\n                                                                                                columnNumber: 33\n                                                                                            }, this)),\n                                                                                        skillsOptions && skillsOptions.length > 0 && skillsOptions.filter((skill)=>{\n                                                                                            var _profile_skills;\n                                                                                            const skillName = skill.name || skill.label || skill.skillName;\n                                                                                            if (!skillName) return false;\n                                                                                            const matchesSearch = skillName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((s)=>{\n                                                                                                const existingSkillId = typeof s === \"string\" ? s : s.skillId || s._id || s.id || s.name;\n                                                                                                const currentSkillId = skill.skillId || skill._id || skill.id || skill.name;\n                                                                                                return existingSkillId === currentSkillId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                                            children: \"No matching skills\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 891,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 798,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 783,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                            variant: \"outline\",\n                                                                            type: \"button\",\n                                                                            size: \"icon\",\n                                                                            className: \"ml-2\",\n                                                                            disabled: !tmpSkill,\n                                                                            onClick: ()=>{\n                                                                                handleAddSkill();\n                                                                                setTmpSkill(\"\");\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 909,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 897,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 782,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                                    children: profile.skills && profile.skills.length > 0 ? profile.skills.map((skill, index)=>{\n                                                                        const skillId = typeof skill === \"string\" ? skill : skill.skillId || skill._id || skill.id || skill.name;\n                                                                        const skillName = getSkillNameById(skillId);\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                                            children: [\n                                                                                skillName,\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"ml-2 h-3 w-3 cursor-pointer\",\n                                                                                    onClick: ()=>handleDeleteSkill(skillId)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 930,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 925,\n                                                                            columnNumber: 29\n                                                                        }, this);\n                                                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm\",\n                                                                        children: \"No skills selected\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 938,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 912,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    children: \"Domains\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 945,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                            onValueChange: (value)=>{\n                                                                                setTmpDomain(value);\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            value: tmpDomain || \"\",\n                                                                            onOpenChange: (open)=>{\n                                                                                if (!open) setSearchQuery(\"\");\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                        placeholder: tmpDomain ? tmpDomain : \"Select domain\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                        lineNumber: 958,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 957,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"text\",\n                                                                                                    value: searchQuery,\n                                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                                    placeholder: \"Search domains\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 967,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 975,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 966,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        domainsOptions && domainsOptions.length > 0 && domainsOptions.filter((domain)=>{\n                                                                                            var _profile_domains;\n                                                                                            const domainName = domain.name || domain.label || domain.domainName;\n                                                                                            if (!domainName) return false;\n                                                                                            const matchesSearch = domainName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((d)=>{\n                                                                                                const existingDomainId = typeof d === \"string\" ? d : d.domainId || d._id || d.id || d.name;\n                                                                                                const currentDomainId = domain.domainId || domain._id || domain.id || domain.name;\n                                                                                                return existingDomainId === currentDomainId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).map((domain, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                value: domain.name || domain.label || domain.domainName,\n                                                                                                children: domain.name || domain.label || domain.domainName\n                                                                                            }, domain.domainId || domain._id || domain.id || index, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                lineNumber: 1015,\n                                                                                                columnNumber: 33\n                                                                                            }, this)),\n                                                                                        domainsOptions && domainsOptions.length > 0 && domainsOptions.filter((domain)=>{\n                                                                                            var _profile_domains;\n                                                                                            const domainName = domain.name || domain.label || domain.domainName;\n                                                                                            if (!domainName) return false;\n                                                                                            const matchesSearch = domainName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((d)=>{\n                                                                                                const existingDomainId = typeof d === \"string\" ? d : d.domainId || d._id || d.id || d.name;\n                                                                                                const currentDomainId = domain.domainId || domain._id || domain.id || domain.name;\n                                                                                                return existingDomainId === currentDomainId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                                            children: \"No matching domains\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 1064,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 964,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 947,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                            variant: \"outline\",\n                                                                            type: \"button\",\n                                                                            size: \"icon\",\n                                                                            className: \"ml-2\",\n                                                                            disabled: !tmpDomain,\n                                                                            onClick: ()=>{\n                                                                                handleAddDomain();\n                                                                                setTmpDomain(\"\");\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 1082,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1070,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 946,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                                    children: profile.domains && profile.domains.length > 0 ? profile.domains.map((domain, index)=>{\n                                                                        const domainId = typeof domain === \"string\" ? domain : domain.domainId || domain._id || domain.id || domain.name;\n                                                                        const domainName = getDomainNameById(domainId);\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                                            children: [\n                                                                                domainName,\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"ml-2 h-3 w-3 cursor-pointer\",\n                                                                                    onClick: ()=>handleDeleteDomain(domainId)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1103,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1098,\n                                                                            columnNumber: 29\n                                                                        }, this);\n                                                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm\",\n                                                                        children: \"No domains selected\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 1111,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1085,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1119,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"githubLink\",\n                                                                    children: \"GitHub Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1124,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"githubLink\",\n                                                                    value: editingProfileData.githubLink || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"githubLink\", e.target.value),\n                                                                    placeholder: \"https://github.com/username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1125,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1123,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"linkedinLink\",\n                                                                    children: \"LinkedIn Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1135,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"linkedinLink\",\n                                                                    value: editingProfileData.linkedinLink || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"linkedinLink\", e.target.value),\n                                                                    placeholder: \"https://linkedin.com/in/username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1136,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1134,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"personalWebsite\",\n                                                                    children: \"Personal Website\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1149,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"personalWebsite\",\n                                                                    value: editingProfileData.personalWebsite || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"personalWebsite\", e.target.value),\n                                                                    placeholder: \"https://yourwebsite.com\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1150,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1148,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"availability\",\n                                                                    children: \"Availability\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1160,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                    value: editingProfileData.availability || \"FREELANCE\",\n                                                                    onValueChange: (value)=>handleInputChange(\"availability\", value),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                placeholder: \"Select availability\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 1168,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1167,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"FULL_TIME\",\n                                                                                    children: \"Full Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1171,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"PART_TIME\",\n                                                                                    children: \"Part Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1172,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"CONTRACT\",\n                                                                                    children: \"Contract\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1173,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"FREELANCE\",\n                                                                                    children: \"Freelance\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1174,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1170,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1161,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1159,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1147,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                        className: \"text-xl font-semibold\",\n                                                        children: \"Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1186,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowProjectDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1194,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Projects\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1185,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            children: profile.projects && profile.projects.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4\",\n                                                children: profile.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cards_freelancerProjectCard__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                ...project,\n                                                                onClick: ()=>handleProjectClick(project)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1204,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleRemoveProject(project._id);\n                                                                },\n                                                                className: \"absolute top-2 right-2 z-10 h-8 w-8 p-0 bg-red-500/80 hover:bg-red-600/90 text-white rounded-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1218,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1209,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, project._id || index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1203,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1201,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                className: \"flex flex-col items-center justify-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-4\",\n                                                        children: \"No projects added to this profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1225,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowProjectDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1233,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Add Projects\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1228,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                        className: \"text-xl font-semibold\",\n                                                        children: \"Experience\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1245,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowExperienceDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1253,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Experience\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1244,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            children: profile.experiences && profile.experiences.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: profile.experiences.map((experience, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                        className: \"p-4 bg-background border\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-lg mb-1\",\n                                                                            children: experience.jobTitle || experience.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1269,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground mb-2\",\n                                                                            children: experience.company\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1272,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mb-3\",\n                                                                            children: [\n                                                                                new Date(experience.workFrom).toLocaleDateString(\"en-US\", {\n                                                                                    year: \"numeric\",\n                                                                                    month: \"short\"\n                                                                                }),\n                                                                                \" \",\n                                                                                \"-\",\n                                                                                \" \",\n                                                                                new Date(experience.workTo).toLocaleDateString(\"en-US\", {\n                                                                                    year: \"numeric\",\n                                                                                    month: \"short\"\n                                                                                })\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1275,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        experience.workDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-foreground\",\n                                                                            children: experience.workDescription\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1292,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1268,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRemoveExperience(experience._id),\n                                                                    className: \"text-destructive hover:text-destructive/80 ml-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 1305,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1297,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1267,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, experience._id || index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1263,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                className: \"flex flex-col items-center justify-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-4\",\n                                                        children: \"No experience added to this profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1314,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowExperienceDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1322,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Add Experience\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1317,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1313,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                        lineNumber: 643,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 631,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_ProjectSelectionDialog__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                open: showProjectDialog,\n                onOpenChange: setShowProjectDialog,\n                freelancerId: user.uid,\n                currentProfileId: profileId,\n                onSuccess: ()=>{\n                    fetchProfile();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_ExperienceSelectionDialog__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                open: showExperienceDialog,\n                onOpenChange: setShowExperienceDialog,\n                freelancerId: user.uid,\n                currentProfileId: profileId,\n                onSuccess: ()=>{\n                    fetchProfile();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1344,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1359,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1357,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: handleDeleteProfile,\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1364,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 1356,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1355,\n                columnNumber: 7\n            }, this),\n            selectedProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                open: isProjectDetailsOpen,\n                onOpenChange: handleCloseProjectDetails,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                    className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                className: \"text-2xl font-bold\",\n                                children: selectedProject.projectName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 1386,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1385,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                selectedProject.thumbnail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: selectedProject.thumbnail,\n                                        alt: \"\".concat(selectedProject.projectName, \" thumbnail\"),\n                                        className: \"w-full h-64 object-cover rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 1395,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1394,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1407,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: selectedProject.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1408,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1406,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1414,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: selectedProject.role\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1415,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1413,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Project Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1421,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: selectedProject.projectType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1422,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1420,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1405,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Duration\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1430,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: [\n                                                                new Date(selectedProject.start).toLocaleDateString(),\n                                                                \" -\",\n                                                                \" \",\n                                                                new Date(selectedProject.end).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1431,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1429,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Technologies Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1438,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: (_selectedProject_techUsed = selectedProject.techUsed) === null || _selectedProject_techUsed === void 0 ? void 0 : _selectedProject_techUsed.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    children: tech\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1444,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1441,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1437,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Links\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1453,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                selectedProject.githubLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: selectedProject.githubLink,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800\",\n                                                                    children: \"GitHub Repository\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1456,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedProject.liveDemoLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: selectedProject.liveDemoLink,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800\",\n                                                                    children: \"Live Demo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1466,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1454,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1452,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1428,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1404,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1391,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 1384,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1380,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n        lineNumber: 624,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfileDetailPage, \"xfNSYa67OHOkItQOXA1jvbqgfpY=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_20__.useSelector,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = ProfileDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProfileDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/[profileId]/page.tsx\n"));

/***/ })

});