"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/stackblur-canvas";
exports.ids = ["vendor-chunks/stackblur-canvas"];
exports.modules = {

/***/ "(ssr)/./node_modules/stackblur-canvas/dist/stackblur-es.js":
/*!************************************************************!*\
  !*** ./node_modules/stackblur-canvas/dist/stackblur-es.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlurStack: () => (/* binding */ BlurStack),\n/* harmony export */   canvasRGB: () => (/* binding */ processCanvasRGB),\n/* harmony export */   canvasRGBA: () => (/* binding */ processCanvasRGBA),\n/* harmony export */   image: () => (/* binding */ processImage),\n/* harmony export */   imageDataRGB: () => (/* binding */ processImageDataRGB),\n/* harmony export */   imageDataRGBA: () => (/* binding */ processImageDataRGBA)\n/* harmony export */ });\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n/* eslint-disable no-bitwise -- used for calculations */\n\n/* eslint-disable unicorn/prefer-query-selector -- aiming at\n  backward-compatibility */\n\n/**\n* StackBlur - a fast almost Gaussian Blur For Canvas\n*\n* In case you find this class useful - especially in commercial projects -\n* I am not totally unhappy for a small donation to my PayPal account\n* <EMAIL>\n*\n* Or support me on flattr:\n* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}.\n*\n* @module StackBlur\n* <AUTHOR> Klingemann\n* Contact: <EMAIL>\n* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}\n* Twitter: @quasimondo\n*\n* @copyright (c) 2010 Mario Klingemann\n*\n* Permission is hereby granted, free of charge, to any person\n* obtaining a copy of this software and associated documentation\n* files (the \"Software\"), to deal in the Software without\n* restriction, including without limitation the rights to use,\n* copy, modify, merge, publish, distribute, sublicense, and/or sell\n* copies of the Software, and to permit persons to whom the\n* Software is furnished to do so, subject to the following\n* conditions:\n*\n* The above copyright notice and this permission notice shall be\n* included in all copies or substantial portions of the Software.\n*\n* THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n* OTHER DEALINGS IN THE SOFTWARE.\n*/\nvar mulTable = [512, 512, 456, 512, 328, 456, 335, 512, 405, 328, 271, 456, 388, 335, 292, 512, 454, 405, 364, 328, 298, 271, 496, 456, 420, 388, 360, 335, 312, 292, 273, 512, 482, 454, 428, 405, 383, 364, 345, 328, 312, 298, 284, 271, 259, 496, 475, 456, 437, 420, 404, 388, 374, 360, 347, 335, 323, 312, 302, 292, 282, 273, 265, 512, 497, 482, 468, 454, 441, 428, 417, 405, 394, 383, 373, 364, 354, 345, 337, 328, 320, 312, 305, 298, 291, 284, 278, 271, 265, 259, 507, 496, 485, 475, 465, 456, 446, 437, 428, 420, 412, 404, 396, 388, 381, 374, 367, 360, 354, 347, 341, 335, 329, 323, 318, 312, 307, 302, 297, 292, 287, 282, 278, 273, 269, 265, 261, 512, 505, 497, 489, 482, 475, 468, 461, 454, 447, 441, 435, 428, 422, 417, 411, 405, 399, 394, 389, 383, 378, 373, 368, 364, 359, 354, 350, 345, 341, 337, 332, 328, 324, 320, 316, 312, 309, 305, 301, 298, 294, 291, 287, 284, 281, 278, 274, 271, 268, 265, 262, 259, 257, 507, 501, 496, 491, 485, 480, 475, 470, 465, 460, 456, 451, 446, 442, 437, 433, 428, 424, 420, 416, 412, 408, 404, 400, 396, 392, 388, 385, 381, 377, 374, 370, 367, 363, 360, 357, 354, 350, 347, 344, 341, 338, 335, 332, 329, 326, 323, 320, 318, 315, 312, 310, 307, 304, 302, 299, 297, 294, 292, 289, 287, 285, 282, 280, 278, 275, 273, 271, 269, 267, 265, 263, 261, 259];\nvar shgTable = [9, 11, 12, 13, 13, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 17, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24];\n/**\n * @param {string|HTMLImageElement} img\n * @param {string|HTMLCanvasElement} canvas\n * @param {Float} radius\n * @param {boolean} blurAlphaChannel\n * @param {boolean} useOffset\n * @param {boolean} skipStyles\n * @returns {undefined}\n */\n\nfunction processImage(img, canvas, radius, blurAlphaChannel, useOffset, skipStyles) {\n  if (typeof img === 'string') {\n    img = document.getElementById(img);\n  }\n\n  if (!img || Object.prototype.toString.call(img).slice(8, -1) === 'HTMLImageElement' && !('naturalWidth' in img)) {\n    return;\n  }\n\n  var dimensionType = useOffset ? 'offset' : 'natural';\n  var w = img[dimensionType + 'Width'];\n  var h = img[dimensionType + 'Height']; // add ImageBitmap support,can blur texture source\n\n  if (Object.prototype.toString.call(img).slice(8, -1) === 'ImageBitmap') {\n    w = img.width;\n    h = img.height;\n  }\n\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || !('getContext' in canvas)) {\n    return;\n  }\n\n  if (!skipStyles) {\n    canvas.style.width = w + 'px';\n    canvas.style.height = h + 'px';\n  }\n\n  canvas.width = w;\n  canvas.height = h;\n  var context = canvas.getContext('2d');\n  context.clearRect(0, 0, w, h);\n  context.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, w, h);\n\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  if (blurAlphaChannel) {\n    processCanvasRGBA(canvas, 0, 0, w, h, radius);\n  } else {\n    processCanvasRGB(canvas, 0, 0, w, h, radius);\n  }\n}\n/**\n * @param {string|HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @throws {Error|TypeError}\n * @returns {ImageData} See {@link https://html.spec.whatwg.org/multipage/canvas.html#imagedata}\n */\n\n\nfunction getImageDataFromCanvas(canvas, topX, topY, width, height) {\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || _typeof(canvas) !== 'object' || !('getContext' in canvas)) {\n    throw new TypeError('Expecting canvas with `getContext` method ' + 'in processCanvasRGB(A) calls!');\n  }\n\n  var context = canvas.getContext('2d');\n\n  try {\n    return context.getImageData(topX, topY, width, height);\n  } catch (e) {\n    throw new Error('unable to access image data: ' + e);\n  }\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGBA(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGBA(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGBA(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null,\n      stackOut = null,\n      yw = 0,\n      yi = 0;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n\n  for (var y = 0; y < height; y++) {\n    stack = stackStart;\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        pa = pixels[yi + 3];\n\n    for (var _i = 0; _i < radiusPlus1; _i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0,\n        aInSum = 0,\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        aOutSum = radiusPlus1 * pa,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb,\n        aSum = sumFactor * pa;\n\n    for (var _i2 = 1; _i2 < radiusPlus1; _i2++) {\n      var p = yi + ((widthMinus1 < _i2 ? widthMinus1 : _i2) << 2);\n      var r = pixels[p],\n          g = pixels[p + 1],\n          b = pixels[p + 2],\n          a = pixels[p + 3];\n      var rbs = radiusPlus1 - _i2;\n      rSum += (stack.r = r) * rbs;\n      gSum += (stack.g = g) * rbs;\n      bSum += (stack.b = b) * rbs;\n      aSum += (stack.a = a) * rbs;\n      rInSum += r;\n      gInSum += g;\n      bInSum += b;\n      aInSum += a;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      var paInitial = aSum * mulSum >>> shgSum;\n      pixels[yi + 3] = paInitial;\n\n      if (paInitial !== 0) {\n        var _a2 = 255 / paInitial;\n\n        pixels[yi] = (rSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 1] = (gSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 2] = (bSum * mulSum >>> shgSum) * _a2;\n      } else {\n        pixels[yi] = pixels[yi + 1] = pixels[yi + 2] = 0;\n      }\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n\n      var _p = x + radius + 1;\n\n      _p = yw + (_p < widthMinus1 ? _p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[_p];\n      gInSum += stackIn.g = pixels[_p + 1];\n      bInSum += stackIn.b = pixels[_p + 2];\n      aInSum += stackIn.a = pixels[_p + 3];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      aSum += aInSum;\n      stackIn = stackIn.next;\n      var _stackOut = stackOut,\n          _r = _stackOut.r,\n          _g = _stackOut.g,\n          _b = _stackOut.b,\n          _a = _stackOut.a;\n      rOutSum += _r;\n      gOutSum += _g;\n      bOutSum += _b;\n      aOutSum += _a;\n      rInSum -= _r;\n      gInSum -= _g;\n      bInSum -= _b;\n      aInSum -= _a;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x = 0; _x < width; _x++) {\n    yi = _x << 2;\n\n    var _pr = pixels[yi],\n        _pg = pixels[yi + 1],\n        _pb = pixels[yi + 2],\n        _pa = pixels[yi + 3],\n        _rOutSum = radiusPlus1 * _pr,\n        _gOutSum = radiusPlus1 * _pg,\n        _bOutSum = radiusPlus1 * _pb,\n        _aOutSum = radiusPlus1 * _pa,\n        _rSum = sumFactor * _pr,\n        _gSum = sumFactor * _pg,\n        _bSum = sumFactor * _pb,\n        _aSum = sumFactor * _pa;\n\n    stack = stackStart;\n\n    for (var _i3 = 0; _i3 < radiusPlus1; _i3++) {\n      stack.r = _pr;\n      stack.g = _pg;\n      stack.b = _pb;\n      stack.a = _pa;\n      stack = stack.next;\n    }\n\n    var yp = width;\n    var _gInSum = 0,\n        _bInSum = 0,\n        _aInSum = 0,\n        _rInSum = 0;\n\n    for (var _i4 = 1; _i4 <= radius; _i4++) {\n      yi = yp + _x << 2;\n\n      var _rbs = radiusPlus1 - _i4;\n\n      _rSum += (stack.r = _pr = pixels[yi]) * _rbs;\n      _gSum += (stack.g = _pg = pixels[yi + 1]) * _rbs;\n      _bSum += (stack.b = _pb = pixels[yi + 2]) * _rbs;\n      _aSum += (stack.a = _pa = pixels[yi + 3]) * _rbs;\n      _rInSum += _pr;\n      _gInSum += _pg;\n      _bInSum += _pb;\n      _aInSum += _pa;\n      stack = stack.next;\n\n      if (_i4 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y = 0; _y < height; _y++) {\n      var _p2 = yi << 2;\n\n      pixels[_p2 + 3] = _pa = _aSum * mulSum >>> shgSum;\n\n      if (_pa > 0) {\n        _pa = 255 / _pa;\n        pixels[_p2] = (_rSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 1] = (_gSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 2] = (_bSum * mulSum >>> shgSum) * _pa;\n      } else {\n        pixels[_p2] = pixels[_p2 + 1] = pixels[_p2 + 2] = 0;\n      }\n\n      _rSum -= _rOutSum;\n      _gSum -= _gOutSum;\n      _bSum -= _bOutSum;\n      _aSum -= _aOutSum;\n      _rOutSum -= stackIn.r;\n      _gOutSum -= stackIn.g;\n      _bOutSum -= stackIn.b;\n      _aOutSum -= stackIn.a;\n      _p2 = _x + ((_p2 = _y + radiusPlus1) < heightMinus1 ? _p2 : heightMinus1) * width << 2;\n      _rSum += _rInSum += stackIn.r = pixels[_p2];\n      _gSum += _gInSum += stackIn.g = pixels[_p2 + 1];\n      _bSum += _bInSum += stackIn.b = pixels[_p2 + 2];\n      _aSum += _aInSum += stackIn.a = pixels[_p2 + 3];\n      stackIn = stackIn.next;\n      _rOutSum += _pr = stackOut.r;\n      _gOutSum += _pg = stackOut.g;\n      _bOutSum += _pb = stackOut.b;\n      _aOutSum += _pa = stackOut.a;\n      _rInSum -= _pr;\n      _gInSum -= _pg;\n      _bInSum -= _pb;\n      _aInSum -= _pa;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGB(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGB(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGB(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null;\n  var stackOut = null;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n  var p, rbs;\n  var yw = 0,\n      yi = 0;\n\n  for (var y = 0; y < height; y++) {\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb;\n    stack = stackStart;\n\n    for (var _i5 = 0; _i5 < radiusPlus1; _i5++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0;\n\n    for (var _i6 = 1; _i6 < radiusPlus1; _i6++) {\n      p = yi + ((widthMinus1 < _i6 ? widthMinus1 : _i6) << 2);\n      rSum += (stack.r = pr = pixels[p]) * (rbs = radiusPlus1 - _i6);\n      gSum += (stack.g = pg = pixels[p + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[p + 2]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      pixels[yi] = rSum * mulSum >>> shgSum;\n      pixels[yi + 1] = gSum * mulSum >>> shgSum;\n      pixels[yi + 2] = bSum * mulSum >>> shgSum;\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      p = yw + ((p = x + radius + 1) < widthMinus1 ? p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[p];\n      gInSum += stackIn.g = pixels[p + 1];\n      bInSum += stackIn.b = pixels[p + 2];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x2 = 0; _x2 < width; _x2++) {\n    yi = _x2 << 2;\n\n    var _pr2 = pixels[yi],\n        _pg2 = pixels[yi + 1],\n        _pb2 = pixels[yi + 2],\n        _rOutSum2 = radiusPlus1 * _pr2,\n        _gOutSum2 = radiusPlus1 * _pg2,\n        _bOutSum2 = radiusPlus1 * _pb2,\n        _rSum2 = sumFactor * _pr2,\n        _gSum2 = sumFactor * _pg2,\n        _bSum2 = sumFactor * _pb2;\n\n    stack = stackStart;\n\n    for (var _i7 = 0; _i7 < radiusPlus1; _i7++) {\n      stack.r = _pr2;\n      stack.g = _pg2;\n      stack.b = _pb2;\n      stack = stack.next;\n    }\n\n    var _rInSum2 = 0,\n        _gInSum2 = 0,\n        _bInSum2 = 0;\n\n    for (var _i8 = 1, yp = width; _i8 <= radius; _i8++) {\n      yi = yp + _x2 << 2;\n      _rSum2 += (stack.r = _pr2 = pixels[yi]) * (rbs = radiusPlus1 - _i8);\n      _gSum2 += (stack.g = _pg2 = pixels[yi + 1]) * rbs;\n      _bSum2 += (stack.b = _pb2 = pixels[yi + 2]) * rbs;\n      _rInSum2 += _pr2;\n      _gInSum2 += _pg2;\n      _bInSum2 += _pb2;\n      stack = stack.next;\n\n      if (_i8 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x2;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y2 = 0; _y2 < height; _y2++) {\n      p = yi << 2;\n      pixels[p] = _rSum2 * mulSum >>> shgSum;\n      pixels[p + 1] = _gSum2 * mulSum >>> shgSum;\n      pixels[p + 2] = _bSum2 * mulSum >>> shgSum;\n      _rSum2 -= _rOutSum2;\n      _gSum2 -= _gOutSum2;\n      _bSum2 -= _bOutSum2;\n      _rOutSum2 -= stackIn.r;\n      _gOutSum2 -= stackIn.g;\n      _bOutSum2 -= stackIn.b;\n      p = _x2 + ((p = _y2 + radiusPlus1) < heightMinus1 ? p : heightMinus1) * width << 2;\n      _rSum2 += _rInSum2 += stackIn.r = pixels[p];\n      _gSum2 += _gInSum2 += stackIn.g = pixels[p + 1];\n      _bSum2 += _bInSum2 += stackIn.b = pixels[p + 2];\n      stackIn = stackIn.next;\n      _rOutSum2 += _pr2 = stackOut.r;\n      _gOutSum2 += _pg2 = stackOut.g;\n      _bOutSum2 += _pb2 = stackOut.b;\n      _rInSum2 -= _pr2;\n      _gInSum2 -= _pg2;\n      _bInSum2 -= _pb2;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n *\n */\n\n\nvar BlurStack =\n/**\n * Set properties.\n */\nfunction BlurStack() {\n  _classCallCheck(this, BlurStack);\n\n  this.r = 0;\n  this.g = 0;\n  this.b = 0;\n  this.a = 0;\n  this.next = null;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stackblur-canvas/dist/stackblur-es.js\n");

/***/ })

};
;