"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/[profileId]/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/[profileId]/page.tsx":
/*!*******************************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/[profileId]/page.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfileDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_cards_freelancerProjectCard__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/cards/freelancerProjectCard */ \"(app-pages-browser)/./src/components/cards/freelancerProjectCard.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_dialogs_ProjectSelectionDialog__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/dialogs/ProjectSelectionDialog */ \"(app-pages-browser)/./src/components/dialogs/ProjectSelectionDialog.tsx\");\n/* harmony import */ var _components_dialogs_ExperienceSelectionDialog__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/dialogs/ExperienceSelectionDialog */ \"(app-pages-browser)/./src/components/dialogs/ExperienceSelectionDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProfileDetailPage() {\n    var _selectedProject_techUsed;\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_20__.useSelector)((state)=>state.user);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const profileId = params.profileId;\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProfileData, setEditingProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [skillsOptions, setSkillsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [domainsOptions, setDomainsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [freelancerSkillsOptions, setFreelancerSkillsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [freelancerDomainsOptions, setFreelancerDomainsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [skillsAndDomainsLoaded, setSkillsAndDomainsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProjectDialog, setShowProjectDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExperienceDialog, setShowExperienceDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [freelancerProjects, setFreelancerProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProjectDetailsOpen, setIsProjectDetailsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tmpSkill, setTmpSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tmpDomain, setTmpDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (profileId) {\n            fetchSkillsAndDomains().then(()=>{\n                fetchProfile();\n                fetchFreelancerProjects();\n            });\n        }\n    }, [\n        profileId,\n        user.uid\n    ]);\n    // Helper function to get skill name from ID\n    const getSkillNameById = (skillId)=>{\n        if (!skillId || !skillsOptions || skillsOptions.length === 0) {\n            return skillId || \"\";\n        }\n        // Try multiple matching strategies\n        let skill = skillsOptions.find((s)=>s.skillId === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s._id === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s.id === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s.name === skillId);\n        // If still not found, try case-insensitive name matching\n        if (!skill) {\n            skill = skillsOptions.find((s)=>{\n                var _this;\n                return ((_this = s.name || s.label || s.skillName) === null || _this === void 0 ? void 0 : _this.toLowerCase()) === skillId.toLowerCase();\n            });\n        }\n        return skill ? skill.name || skill.label || skill.skillName || skillId : skillId;\n    };\n    // Helper function to get domain name from ID\n    const getDomainNameById = (domainId)=>{\n        if (!domainId || !domainsOptions || domainsOptions.length === 0) {\n            return domainId || \"\";\n        }\n        // Try multiple matching strategies\n        let domain = domainsOptions.find((d)=>d.domainId === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d._id === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d.id === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d.name === domainId);\n        // If still not found, try case-insensitive name matching\n        if (!domain) {\n            domain = domainsOptions.find((d)=>{\n                var _this;\n                return ((_this = d.name || d.label || d.domainName) === null || _this === void 0 ? void 0 : _this.toLowerCase()) === domainId.toLowerCase();\n            });\n        }\n        return domain ? domain.name || domain.label || domain.domainName || domainId : domainId;\n    };\n    // Helper function to transform profile data for backend API\n    const transformProfileForAPI = (profileData)=>{\n        var _profileData_skills, _profileData_domains;\n        const transformedSkills = ((_profileData_skills = profileData.skills) === null || _profileData_skills === void 0 ? void 0 : _profileData_skills.map((skill)=>{\n            if (typeof skill === \"string\") {\n                return skill;\n            }\n            // Prioritize skillId field, then fallback to other ID fields\n            const skillId = skill.skillId || skill._id || skill.id || skill.value || skill.name;\n            return skillId;\n        })) || [];\n        const transformedDomains = ((_profileData_domains = profileData.domains) === null || _profileData_domains === void 0 ? void 0 : _profileData_domains.map((domain)=>{\n            if (typeof domain === \"string\") {\n                return domain;\n            }\n            // Prioritize domainId field, then fallback to other ID fields\n            const domainId = domain.domainId || domain._id || domain.id || domain.value || domain.name;\n            return domainId;\n        })) || [];\n        return {\n            ...profileData,\n            skills: transformedSkills,\n            domains: transformedDomains\n        };\n    };\n    const fetchProfile = async ()=>{\n        if (!profileId) return;\n        setIsLoading(true);\n        try {\n            var _profileData_skills, _profileData_domains;\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/profile/\".concat(profileId));\n            const profileData = response.data.data;\n            // If profile has projects, fetch complete project data to ensure we have thumbnails\n            if (profileData.projects && profileData.projects.length > 0) {\n                try {\n                    const freelancerResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid));\n                    const freelancerData = freelancerResponse.data.data || {};\n                    const freelancerProjects = freelancerData.projects || {};\n                    // Convert projects object to array if it's an object\n                    const allFreelancerProjects = Array.isArray(freelancerProjects) ? freelancerProjects : Object.values(freelancerProjects);\n                    // Merge profile projects with complete freelancer project data\n                    const enrichedProjects = profileData.projects.map((profileProject)=>{\n                        const fullProject = allFreelancerProjects.find((fp)=>fp._id === profileProject._id);\n                        // Use full project data if available, otherwise use profile project data\n                        return fullProject || profileProject;\n                    });\n                    profileData.projects = enrichedProjects;\n                } catch (projectError) {\n                    console.warn(\"Could not fetch complete project data:\", projectError);\n                // Continue with existing profile data if project fetch fails\n                }\n            }\n            // Debug: Log the raw profile data to understand the structure\n            console.log(\"=== PROFILE DEBUG ===\");\n            console.log(\"Raw profile data from API:\", profileData);\n            console.log(\"Profile skills:\", profileData.skills);\n            console.log(\"Profile domains:\", profileData.domains);\n            console.log(\"Skills type:\", typeof ((_profileData_skills = profileData.skills) === null || _profileData_skills === void 0 ? void 0 : _profileData_skills[0]));\n            console.log(\"Domains type:\", typeof ((_profileData_domains = profileData.domains) === null || _profileData_domains === void 0 ? void 0 : _profileData_domains[0]));\n            console.log(\"=== END DEBUG ===\");\n            // Ensure skills and domains are properly formatted as arrays\n            const processedProfileData = {\n                ...profileData,\n                skills: Array.isArray(profileData.skills) ? profileData.skills : [],\n                domains: Array.isArray(profileData.domains) ? profileData.domains : []\n            };\n            setProfile(processedProfileData);\n            setEditingProfileData(processedProfileData);\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profile\",\n                variant: \"destructive\"\n            });\n            router.push(\"/freelancer/settings/profiles\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const fetchSkillsAndDomains = async ()=>{\n        try {\n            // Fetch both complete collections and freelancer data\n            const [skillsResponse, domainsResponse, freelancerResponse] = await Promise.all([\n                _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/skills\"),\n                _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/domain\"),\n                _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid))\n            ]);\n            // Complete skills and domains for ID resolution\n            const allSkills = skillsResponse.data.data || [];\n            const allDomains = domainsResponse.data.data || [];\n            console.log(\"=== SKILLS & DOMAINS DEBUG ===\");\n            console.log(\"All skills from /skills:\", allSkills);\n            console.log(\"All domains from /domain:\", allDomains);\n            // Freelancer's personal skills and domains for dropdown\n            const freelancerData = freelancerResponse.data.data || {};\n            const freelancerSkills = freelancerData.skills || [];\n            const freelancerDomains = freelancerData.domain || [];\n            console.log(\"Freelancer skills:\", freelancerSkills);\n            console.log(\"Freelancer domains:\", freelancerDomains);\n            console.log(\"=== END SKILLS & DOMAINS DEBUG ===\");\n            // Set all data\n            setSkillsOptions(allSkills);\n            setDomainsOptions(allDomains);\n            setFreelancerSkillsOptions(freelancerSkills);\n            setFreelancerDomainsOptions(freelancerDomains);\n            setSkillsAndDomainsLoaded(true);\n        } catch (error) {\n            console.error(\"Error fetching skills and domains:\", error);\n        }\n    };\n    const fetchFreelancerProjects = async ()=>{\n        try {\n            const freelancerResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid));\n            const freelancerData = freelancerResponse.data.data || {};\n            const projectsData = freelancerData.projects || {};\n            setFreelancerProjects(projectsData);\n        } catch (error) {\n            console.error(\"Error fetching freelancer projects:\", error);\n        }\n    };\n    const handleUpdateProfile = async ()=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        // Client-side validation\n        if (!editingProfileData.profileName || editingProfileData.profileName.trim().length === 0) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Profile name is required\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!editingProfileData.description || editingProfileData.description.trim().length < 10) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Description must be at least 10 characters long\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (editingProfileData.profileName.length > 100) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Profile name must be less than 100 characters\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (editingProfileData.description.length > 500) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Description must be less than 500 characters\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsUpdating(true);\n        try {\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI(editingProfileData);\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Profile updated successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setEditingProfileData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleAddSkill = ()=>{\n        var _profile_skills;\n        if (!tmpSkill || !profile || !freelancerSkillsOptions || freelancerSkillsOptions.length === 0) return;\n        // tmpSkill is now the skill name directly from freelancer's skills\n        const skillNameToAdd = tmpSkill;\n        // Check if skill is already added\n        const isAlreadyAdded = (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((skill)=>{\n            const existingSkillId = typeof skill === \"string\" ? skill : skill.skillId || skill._id || skill.id || skill.name;\n            return existingSkillId === skillNameToAdd;\n        });\n        if (!isAlreadyAdded) {\n            const updatedSkills = [\n                ...profile.skills || [],\n                skillNameToAdd\n            ];\n            setProfile({\n                ...profile,\n                skills: updatedSkills\n            });\n            setEditingProfileData({\n                ...editingProfileData,\n                skills: updatedSkills\n            });\n        }\n        setTmpSkill(\"\");\n        setSearchQuery(\"\");\n    };\n    const handleAddDomain = ()=>{\n        var _profile_domains;\n        if (!tmpDomain || !profile || !freelancerDomainsOptions || freelancerDomainsOptions.length === 0) return;\n        // tmpDomain is now the domain name directly from freelancer's domains\n        const domainNameToAdd = tmpDomain;\n        // Check if domain is already added\n        const isAlreadyAdded = (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((domain)=>{\n            const existingDomainId = typeof domain === \"string\" ? domain : domain.domainId || domain._id || domain.id || domain.name;\n            return existingDomainId === domainNameToAdd;\n        });\n        if (!isAlreadyAdded) {\n            const updatedDomains = [\n                ...profile.domains || [],\n                domainNameToAdd\n            ];\n            setProfile({\n                ...profile,\n                domains: updatedDomains\n            });\n            setEditingProfileData({\n                ...editingProfileData,\n                domains: updatedDomains\n            });\n        }\n        setTmpDomain(\"\");\n        setSearchQuery(\"\");\n    };\n    const handleDeleteSkill = (skillIdToDelete)=>{\n        if (!profile || !profile.skills) return;\n        const updatedSkills = profile.skills.filter((skill)=>{\n            const skillId = typeof skill === \"string\" ? skill : skill.skillId || skill._id || skill.id || skill.name;\n            return skillId !== skillIdToDelete;\n        });\n        setProfile({\n            ...profile,\n            skills: updatedSkills\n        });\n        setEditingProfileData({\n            ...editingProfileData,\n            skills: updatedSkills\n        });\n    };\n    const handleDeleteDomain = (domainIdToDelete)=>{\n        if (!profile || !profile.domains) return;\n        const updatedDomains = profile.domains.filter((domain)=>{\n            const domainId = typeof domain === \"string\" ? domain : domain.domainId || domain._id || domain.id || domain.name;\n            return domainId !== domainIdToDelete;\n        });\n        setProfile({\n            ...profile,\n            domains: updatedDomains\n        });\n        setEditingProfileData({\n            ...editingProfileData,\n            domains: updatedDomains\n        });\n    };\n    const handleRemoveProject = async (projectId)=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            const updatedProjects = (profile.projects || []).filter((project)=>project._id !== projectId);\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI({\n                ...profile,\n                projects: updatedProjects\n            });\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Project removed from profile successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error removing project:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to remove project from profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleRemoveExperience = async (experienceId)=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            const updatedExperiences = (profile.experiences || []).filter((experience)=>experience._id !== experienceId);\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI({\n                ...profile,\n                experiences: updatedExperiences\n            });\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Experience removed from profile successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error removing experience:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to remove experience from profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleProjectClick = (project)=>{\n        setSelectedProject(project);\n        setIsProjectDetailsOpen(true);\n    };\n    const handleCloseProjectDetails = ()=>{\n        setIsProjectDetailsOpen(false);\n        setSelectedProject(null);\n    };\n    const handleDeleteProfile = async ()=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.delete(\"/freelancer/profile/\".concat(profile._id));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            router.push(\"/freelancer/settings/profiles\");\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n        }\n    };\n    if (isLoading || !skillsAndDomainsLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                    menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                    active: \"Profiles\",\n                    isKycCheck: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                            menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                            activeMenu: \"Profiles\",\n                            breadcrumbItems: [\n                                {\n                                    label: \"Freelancer\",\n                                    link: \"/dashboard/freelancer\"\n                                },\n                                {\n                                    label: \"Settings\",\n                                    link: \"#\"\n                                },\n                                {\n                                    label: \"Profiles\",\n                                    link: \"/freelancer/settings/profiles\"\n                                },\n                                {\n                                    label: \"Profile Details\",\n                                    link: \"#\"\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading profile...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 576,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n            lineNumber: 569,\n            columnNumber: 7\n        }, this);\n    }\n    if (!profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                    menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                    active: \"Profiles\",\n                    isKycCheck: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 601,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                            menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                            activeMenu: \"Profiles\",\n                            breadcrumbItems: [\n                                {\n                                    label: \"Freelancer\",\n                                    link: \"/dashboard/freelancer\"\n                                },\n                                {\n                                    label: \"Settings\",\n                                    link: \"#\"\n                                },\n                                {\n                                    label: \"Profiles\",\n                                    link: \"/freelancer/settings/profiles\"\n                                },\n                                {\n                                    label: \"Profile Details\",\n                                    link: \"#\"\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Profile not found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: ()=>router.push(\"/freelancer/settings/profiles\"),\n                                        className: \"mt-4\",\n                                        children: \"Back to Profiles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n            lineNumber: 600,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 637,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"/freelancer/settings/profiles\"\n                            },\n                            {\n                                label: profile.profileName,\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/freelancer/settings/profiles\"),\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Profiles\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: profile.profileName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Edit your professional profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    onClick: handleUpdateProfile,\n                                                    disabled: isUpdating,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isUpdating ? \"Updating...\" : \"Save Changes\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    variant: \"destructive\",\n                                                    onClick: ()=>setDeleteDialogOpen(true),\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Delete\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Profile Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                            htmlFor: \"profileName\",\n                                                                            children: \"Profile Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 708,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm \".concat((editingProfileData.profileName || \"\").length === 0 ? \"text-red-500\" : (editingProfileData.profileName || \"\").length > 100 ? \"text-red-500\" : \"text-muted-foreground\"),\n                                                                            children: [\n                                                                                (editingProfileData.profileName || \"\").length,\n                                                                                \"/100\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 709,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"profileName\",\n                                                                    value: editingProfileData.profileName || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"profileName\", e.target.value),\n                                                                    placeholder: \"e.g., Frontend Developer\",\n                                                                    className: (editingProfileData.profileName || \"\").length === 0 || (editingProfileData.profileName || \"\").length > 100 ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"hourlyRate\",\n                                                                    children: \"Hourly Rate ($)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"hourlyRate\",\n                                                                    type: \"number\",\n                                                                    value: editingProfileData.hourlyRate || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"hourlyRate\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"description\",\n                                                                    children: \"Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 757,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat((editingProfileData.description || \"\").length < 10 ? \"text-red-500\" : (editingProfileData.description || \"\").length > 500 ? \"text-red-500\" : \"text-muted-foreground\"),\n                                                                    children: [\n                                                                        (editingProfileData.description || \"\").length,\n                                                                        \"/500 (min: 10)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                            id: \"description\",\n                                                            value: editingProfileData.description || \"\",\n                                                            onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                            placeholder: \"Describe your expertise and experience... (minimum 10 characters)\",\n                                                            rows: 4,\n                                                            className: (editingProfileData.description || \"\").length < 10 || (editingProfileData.description || \"\").length > 500 ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    children: \"Skills\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                            onValueChange: (value)=>{\n                                                                                setTmpSkill(value);\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            value: tmpSkill || \"\",\n                                                                            onOpenChange: (open)=>{\n                                                                                if (!open) setSearchQuery(\"\");\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                        placeholder: tmpSkill ? tmpSkill : \"Select skill\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                        lineNumber: 806,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 805,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"text\",\n                                                                                                    value: searchQuery,\n                                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                                    placeholder: \"Search skills\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 813,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 821,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 812,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        freelancerSkillsOptions && freelancerSkillsOptions.length > 0 && freelancerSkillsOptions.filter((skillName)=>{\n                                                                                            var _profile_skills;\n                                                                                            if (!skillName) return false;\n                                                                                            const matchesSearch = skillName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((s)=>{\n                                                                                                const existingSkillId = typeof s === \"string\" ? s : s.skillId || s._id || s.id || s.name;\n                                                                                                return existingSkillId === skillName;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).map((skillName, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                value: skillName,\n                                                                                                children: skillName\n                                                                                            }, index, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                lineNumber: 853,\n                                                                                                columnNumber: 33\n                                                                                            }, this)),\n                                                                                        freelancerSkillsOptions && freelancerSkillsOptions.length > 0 && freelancerSkillsOptions.filter((skillName)=>{\n                                                                                            var _profile_skills;\n                                                                                            if (!skillName) return false;\n                                                                                            const matchesSearch = skillName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((s)=>{\n                                                                                                const existingSkillId = typeof s === \"string\" ? s : s.skillId || s._id || s.id || s.name;\n                                                                                                return existingSkillId === skillName;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                                            children: \"No matching skills\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 881,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 810,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                            variant: \"outline\",\n                                                                            type: \"button\",\n                                                                            size: \"icon\",\n                                                                            className: \"ml-2\",\n                                                                            disabled: !tmpSkill,\n                                                                            onClick: ()=>{\n                                                                                handleAddSkill();\n                                                                                setTmpSkill(\"\");\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 899,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 887,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 794,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                                    children: profile.skills && profile.skills.length > 0 ? profile.skills.map((skill, index)=>{\n                                                                        // Handle both populated objects and ID strings\n                                                                        const skillId = typeof skill === \"string\" ? skill : skill._id;\n                                                                        const skillName = typeof skill === \"string\" ? getSkillNameById(skill) : skill.label || skill.name || skill._id;\n                                                                        console.log(\"Displaying skill:\", {\n                                                                            skill,\n                                                                            skillId,\n                                                                            skillName\n                                                                        });\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                                            children: [\n                                                                                skillName,\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"ml-2 h-3 w-3 cursor-pointer\",\n                                                                                    onClick: ()=>handleDeleteSkill(skillId)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 925,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 920,\n                                                                            columnNumber: 29\n                                                                        }, this);\n                                                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm\",\n                                                                        children: \"No skills selected\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 933,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    children: \"Domains\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                            onValueChange: (value)=>{\n                                                                                setTmpDomain(value);\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            value: tmpDomain || \"\",\n                                                                            onOpenChange: (open)=>{\n                                                                                if (!open) setSearchQuery(\"\");\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                        placeholder: tmpDomain ? tmpDomain : \"Select domain\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                        lineNumber: 953,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 952,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"text\",\n                                                                                                    value: searchQuery,\n                                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                                    placeholder: \"Search domains\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 962,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 970,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 961,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        freelancerDomainsOptions && freelancerDomainsOptions.length > 0 && freelancerDomainsOptions.filter((domainName)=>{\n                                                                                            var _profile_domains;\n                                                                                            if (!domainName) return false;\n                                                                                            const matchesSearch = domainName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((d)=>{\n                                                                                                const existingDomainId = typeof d === \"string\" ? d : d.domainId || d._id || d.id || d.name;\n                                                                                                return existingDomainId === domainName;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).map((domainName, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                value: domainName,\n                                                                                                children: domainName\n                                                                                            }, index, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                lineNumber: 1001,\n                                                                                                columnNumber: 33\n                                                                                            }, this)),\n                                                                                        freelancerDomainsOptions && freelancerDomainsOptions.length > 0 && freelancerDomainsOptions.filter((domainName)=>{\n                                                                                            var _profile_domains;\n                                                                                            if (!domainName) return false;\n                                                                                            const matchesSearch = domainName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((d)=>{\n                                                                                                const existingDomainId = typeof d === \"string\" ? d : d.domainId || d._id || d.id || d.name;\n                                                                                                return existingDomainId === domainName;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                                            children: \"No matching domains\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 1028,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 959,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 942,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                            variant: \"outline\",\n                                                                            type: \"button\",\n                                                                            size: \"icon\",\n                                                                            className: \"ml-2\",\n                                                                            disabled: !tmpDomain,\n                                                                            onClick: ()=>{\n                                                                                handleAddDomain();\n                                                                                setTmpDomain(\"\");\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 1046,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1034,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                                    children: profile.domains && profile.domains.length > 0 ? profile.domains.map((domain, index)=>{\n                                                                        // Handle both populated objects and ID strings\n                                                                        const domainId = typeof domain === \"string\" ? domain : domain._id;\n                                                                        const domainName = typeof domain === \"string\" ? getDomainNameById(domain) : domain.label || domain.name || domain._id;\n                                                                        console.log(\"Displaying domain:\", {\n                                                                            domain,\n                                                                            domainId,\n                                                                            domainName\n                                                                        });\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                                            children: [\n                                                                                domainName,\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"ml-2 h-3 w-3 cursor-pointer\",\n                                                                                    onClick: ()=>handleDeleteDomain(domainId)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1072,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1067,\n                                                                            columnNumber: 29\n                                                                        }, this);\n                                                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm\",\n                                                                        children: \"No domains selected\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 1080,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1049,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1088,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"githubLink\",\n                                                                    children: \"GitHub Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1093,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"githubLink\",\n                                                                    value: editingProfileData.githubLink || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"githubLink\", e.target.value),\n                                                                    placeholder: \"https://github.com/username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1094,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1092,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"linkedinLink\",\n                                                                    children: \"LinkedIn Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1104,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"linkedinLink\",\n                                                                    value: editingProfileData.linkedinLink || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"linkedinLink\", e.target.value),\n                                                                    placeholder: \"https://linkedin.com/in/username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1105,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1103,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1091,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"personalWebsite\",\n                                                                    children: \"Personal Website\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1118,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"personalWebsite\",\n                                                                    value: editingProfileData.personalWebsite || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"personalWebsite\", e.target.value),\n                                                                    placeholder: \"https://yourwebsite.com\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1119,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"availability\",\n                                                                    children: \"Availability\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1129,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                    value: editingProfileData.availability || \"FREELANCE\",\n                                                                    onValueChange: (value)=>handleInputChange(\"availability\", value),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                placeholder: \"Select availability\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 1137,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1136,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"FULL_TIME\",\n                                                                                    children: \"Full Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1140,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"PART_TIME\",\n                                                                                    children: \"Part Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1141,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"CONTRACT\",\n                                                                                    children: \"Contract\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1142,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"FREELANCE\",\n                                                                                    children: \"Freelance\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1143,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1139,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1130,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1128,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1116,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                        className: \"text-xl font-semibold\",\n                                                        children: \"Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1155,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowProjectDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1163,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Projects\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1158,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1154,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            children: profile.projects && profile.projects.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4\",\n                                                children: profile.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cards_freelancerProjectCard__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                ...project,\n                                                                onClick: ()=>handleProjectClick(project)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1173,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleRemoveProject(project._id);\n                                                                },\n                                                                className: \"absolute top-2 right-2 z-10 h-8 w-8 p-0 bg-red-500/80 hover:bg-red-600/90 text-white rounded-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1187,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1178,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, project._id || index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1172,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1170,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                className: \"flex flex-col items-center justify-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-4\",\n                                                        children: \"No projects added to this profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1194,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowProjectDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1202,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Add Projects\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1193,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                        className: \"text-xl font-semibold\",\n                                                        children: \"Experience\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1214,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowExperienceDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1222,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Experience\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1213,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            children: profile.experiences && profile.experiences.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: profile.experiences.map((experience, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                        className: \"p-4 bg-background border\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-lg mb-1\",\n                                                                            children: experience.jobTitle || experience.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1238,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground mb-2\",\n                                                                            children: experience.company\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1241,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mb-3\",\n                                                                            children: [\n                                                                                new Date(experience.workFrom).toLocaleDateString(\"en-US\", {\n                                                                                    year: \"numeric\",\n                                                                                    month: \"short\"\n                                                                                }),\n                                                                                \" \",\n                                                                                \"-\",\n                                                                                \" \",\n                                                                                new Date(experience.workTo).toLocaleDateString(\"en-US\", {\n                                                                                    year: \"numeric\",\n                                                                                    month: \"short\"\n                                                                                })\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1244,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        experience.workDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-foreground\",\n                                                                            children: experience.workDescription\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1261,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1237,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRemoveExperience(experience._id),\n                                                                    className: \"text-destructive hover:text-destructive/80 ml-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 1274,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1266,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1236,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, experience._id || index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1232,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1229,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                className: \"flex flex-col items-center justify-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-4\",\n                                                        children: \"No experience added to this profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1283,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowExperienceDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Add Experience\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1286,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1282,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1227,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1211,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 643,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_ProjectSelectionDialog__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                open: showProjectDialog,\n                onOpenChange: setShowProjectDialog,\n                freelancerId: user.uid,\n                currentProfileId: profileId,\n                onSuccess: ()=>{\n                    fetchProfile();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_ExperienceSelectionDialog__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                open: showExperienceDialog,\n                onOpenChange: setShowExperienceDialog,\n                freelancerId: user.uid,\n                currentProfileId: profileId,\n                onSuccess: ()=>{\n                    fetchProfile();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1334,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: handleDeleteProfile,\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1340,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1333,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 1325,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1324,\n                columnNumber: 7\n            }, this),\n            selectedProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                open: isProjectDetailsOpen,\n                onOpenChange: handleCloseProjectDetails,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                    className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                className: \"text-2xl font-bold\",\n                                children: selectedProject.projectName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 1355,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1354,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                selectedProject.thumbnail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: selectedProject.thumbnail,\n                                        alt: \"\".concat(selectedProject.projectName, \" thumbnail\"),\n                                        className: \"w-full h-64 object-cover rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 1364,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1363,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1376,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: selectedProject.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1377,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1383,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: selectedProject.role\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1384,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1382,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Project Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1390,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: selectedProject.projectType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1391,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1374,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Duration\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: [\n                                                                new Date(selectedProject.start).toLocaleDateString(),\n                                                                \" -\",\n                                                                \" \",\n                                                                new Date(selectedProject.end).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1400,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Technologies Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1407,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: (_selectedProject_techUsed = selectedProject.techUsed) === null || _selectedProject_techUsed === void 0 ? void 0 : _selectedProject_techUsed.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    children: tech\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1413,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1410,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1406,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Links\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1422,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                selectedProject.githubLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: selectedProject.githubLink,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800\",\n                                                                    children: \"GitHub Repository\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1425,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedProject.liveDemoLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: selectedProject.liveDemoLink,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800\",\n                                                                    children: \"Live Demo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1435,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1423,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1421,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1397,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1373,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1360,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 1353,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1349,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n        lineNumber: 636,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfileDetailPage, \"3lh59P/pOledEBOKOE3o+yVj+eQ=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_20__.useSelector,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = ProfileDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProfileDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/[profileId]/page.tsx\n"));

/***/ })

});