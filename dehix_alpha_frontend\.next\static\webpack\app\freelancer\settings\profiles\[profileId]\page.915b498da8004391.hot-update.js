"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/[profileId]/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/[profileId]/page.tsx":
/*!*******************************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/[profileId]/page.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfileDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_cards_freelancerProjectCard__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/cards/freelancerProjectCard */ \"(app-pages-browser)/./src/components/cards/freelancerProjectCard.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_dialogs_ProjectSelectionDialog__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/dialogs/ProjectSelectionDialog */ \"(app-pages-browser)/./src/components/dialogs/ProjectSelectionDialog.tsx\");\n/* harmony import */ var _components_dialogs_ExperienceSelectionDialog__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/dialogs/ExperienceSelectionDialog */ \"(app-pages-browser)/./src/components/dialogs/ExperienceSelectionDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProfileDetailPage() {\n    var _selectedProject_techUsed;\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_20__.useSelector)((state)=>state.user);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const profileId = params.profileId;\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProfileData, setEditingProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [skillsOptions, setSkillsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [domainsOptions, setDomainsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [skillsAndDomainsLoaded, setSkillsAndDomainsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProjectDialog, setShowProjectDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExperienceDialog, setShowExperienceDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [freelancerProjects, setFreelancerProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProjectDetailsOpen, setIsProjectDetailsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tmpSkill, setTmpSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tmpDomain, setTmpDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (profileId) {\n            fetchSkillsAndDomains().then(()=>{\n                fetchProfile();\n                fetchFreelancerProjects();\n            });\n        }\n    }, [\n        profileId,\n        user.uid\n    ]);\n    // Helper function to get skill name from ID\n    const getSkillNameById = (skillId)=>{\n        if (!skillId || !skillsOptions || skillsOptions.length === 0) {\n            return skillId || \"\";\n        }\n        // Try multiple matching strategies\n        let skill = skillsOptions.find((s)=>s.skillId === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s._id === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s.id === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s.name === skillId);\n        // If still not found, try case-insensitive name matching\n        if (!skill) {\n            skill = skillsOptions.find((s)=>{\n                var _this;\n                return ((_this = s.name || s.label || s.skillName) === null || _this === void 0 ? void 0 : _this.toLowerCase()) === skillId.toLowerCase();\n            });\n        }\n        return skill ? skill.name || skill.label || skill.skillName || skillId : skillId;\n    };\n    // Helper function to get domain name from ID\n    const getDomainNameById = (domainId)=>{\n        if (!domainId || !domainsOptions || domainsOptions.length === 0) {\n            return domainId || \"\";\n        }\n        // Try multiple matching strategies\n        let domain = domainsOptions.find((d)=>d.domainId === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d._id === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d.id === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d.name === domainId);\n        // If still not found, try case-insensitive name matching\n        if (!domain) {\n            domain = domainsOptions.find((d)=>{\n                var _this;\n                return ((_this = d.name || d.label || d.domainName) === null || _this === void 0 ? void 0 : _this.toLowerCase()) === domainId.toLowerCase();\n            });\n        }\n        return domain ? domain.name || domain.label || domain.domainName || domainId : domainId;\n    };\n    // Helper function to transform profile data for backend API\n    const transformProfileForAPI = (profileData)=>{\n        var _profileData_skills, _profileData_domains;\n        const transformedSkills = ((_profileData_skills = profileData.skills) === null || _profileData_skills === void 0 ? void 0 : _profileData_skills.map((skill)=>{\n            if (typeof skill === \"string\") {\n                return skill;\n            }\n            // Prioritize skillId field, then fallback to other ID fields\n            const skillId = skill.skillId || skill._id || skill.id || skill.value || skill.name;\n            return skillId;\n        })) || [];\n        const transformedDomains = ((_profileData_domains = profileData.domains) === null || _profileData_domains === void 0 ? void 0 : _profileData_domains.map((domain)=>{\n            if (typeof domain === \"string\") {\n                return domain;\n            }\n            // Prioritize domainId field, then fallback to other ID fields\n            const domainId = domain.domainId || domain._id || domain.id || domain.value || domain.name;\n            return domainId;\n        })) || [];\n        return {\n            ...profileData,\n            skills: transformedSkills,\n            domains: transformedDomains\n        };\n    };\n    const fetchProfile = async ()=>{\n        if (!profileId) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/profile/\".concat(profileId));\n            const profileData = response.data.data;\n            // If profile has projects, fetch complete project data to ensure we have thumbnails\n            if (profileData.projects && profileData.projects.length > 0) {\n                try {\n                    const freelancerResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid));\n                    const freelancerData = freelancerResponse.data.data || {};\n                    const freelancerProjects = freelancerData.projects || {};\n                    // Convert projects object to array if it's an object\n                    const allFreelancerProjects = Array.isArray(freelancerProjects) ? freelancerProjects : Object.values(freelancerProjects);\n                    // Merge profile projects with complete freelancer project data\n                    const enrichedProjects = profileData.projects.map((profileProject)=>{\n                        const fullProject = allFreelancerProjects.find((fp)=>fp._id === profileProject._id);\n                        // Use full project data if available, otherwise use profile project data\n                        return fullProject || profileProject;\n                    });\n                    profileData.projects = enrichedProjects;\n                } catch (projectError) {\n                    console.warn(\"Could not fetch complete project data:\", projectError);\n                // Continue with existing profile data if project fetch fails\n                }\n            }\n            // Ensure skills and domains are properly formatted as arrays of strings\n            const processedProfileData = {\n                ...profileData,\n                skills: Array.isArray(profileData.skills) ? profileData.skills : [],\n                domains: Array.isArray(profileData.domains) ? profileData.domains : []\n            };\n            setProfile(processedProfileData);\n            setEditingProfileData(processedProfileData);\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profile\",\n                variant: \"destructive\"\n            });\n            router.push(\"/freelancer/settings/profiles\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const fetchSkillsAndDomains = async ()=>{\n        try {\n            const freelancerResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid));\n            const freelancerData = freelancerResponse.data.data || {};\n            const skillsData = freelancerData.skills || [];\n            const skillsArray = Array.isArray(skillsData) ? skillsData : [];\n            setSkillsOptions(skillsArray);\n            const domainsData = freelancerData.domain || [];\n            const domainsArray = Array.isArray(domainsData) ? domainsData : [];\n            setDomainsOptions(domainsArray);\n            setSkillsAndDomainsLoaded(true);\n        } catch (error) {\n            console.error(\"Error fetching skills and domains:\", error);\n        }\n    };\n    const fetchFreelancerProjects = async ()=>{\n        try {\n            const freelancerResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid));\n            const freelancerData = freelancerResponse.data.data || {};\n            const projectsData = freelancerData.projects || {};\n            setFreelancerProjects(projectsData);\n        } catch (error) {\n            console.error(\"Error fetching freelancer projects:\", error);\n        }\n    };\n    const handleUpdateProfile = async ()=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        // Client-side validation\n        if (!editingProfileData.profileName || editingProfileData.profileName.trim().length === 0) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Profile name is required\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!editingProfileData.description || editingProfileData.description.trim().length < 10) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Description must be at least 10 characters long\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (editingProfileData.profileName.length > 100) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Profile name must be less than 100 characters\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (editingProfileData.description.length > 500) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Description must be less than 500 characters\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsUpdating(true);\n        try {\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI(editingProfileData);\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Profile updated successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setEditingProfileData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleAddSkill = ()=>{\n        if (!tmpSkill || !profile || !skillsOptions || skillsOptions.length === 0) return;\n        const selectedSkill = skillsOptions.find((skill)=>(skill.name || skill.label || skill.skillName) === tmpSkill);\n        if (selectedSkill) {\n            var _profile_skills;\n            // Add the skillId (string) to the profile, not the entire object\n            const skillIdToAdd = selectedSkill.skillId || selectedSkill._id || selectedSkill.id || selectedSkill.name;\n            // Check if skill is already added by comparing IDs\n            const isAlreadyAdded = (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((skill)=>{\n                const existingSkillId = typeof skill === \"string\" ? skill : skill.skillId || skill._id || skill.id || skill.name;\n                return existingSkillId === skillIdToAdd;\n            });\n            if (!isAlreadyAdded) {\n                const updatedSkills = [\n                    ...profile.skills || [],\n                    skillIdToAdd\n                ];\n                setProfile({\n                    ...profile,\n                    skills: updatedSkills\n                });\n                setEditingProfileData({\n                    ...editingProfileData,\n                    skills: updatedSkills\n                });\n            }\n            setTmpSkill(\"\");\n            setSearchQuery(\"\");\n        }\n    };\n    const handleAddDomain = ()=>{\n        if (!tmpDomain || !profile || !domainsOptions || domainsOptions.length === 0) return;\n        const selectedDomain = domainsOptions.find((domain)=>(domain.name || domain.label || domain.domainName) === tmpDomain);\n        if (selectedDomain) {\n            var _profile_domains;\n            // Add the domainId (string) to the profile, not the entire object\n            const domainIdToAdd = selectedDomain.domainId || selectedDomain._id || selectedDomain.id || selectedDomain.name;\n            // Check if domain is already added by comparing IDs\n            const isAlreadyAdded = (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((domain)=>{\n                const existingDomainId = typeof domain === \"string\" ? domain : domain.domainId || domain._id || domain.id || domain.name;\n                return existingDomainId === domainIdToAdd;\n            });\n            if (!isAlreadyAdded) {\n                const updatedDomains = [\n                    ...profile.domains || [],\n                    domainIdToAdd\n                ];\n                setProfile({\n                    ...profile,\n                    domains: updatedDomains\n                });\n                setEditingProfileData({\n                    ...editingProfileData,\n                    domains: updatedDomains\n                });\n            }\n            setTmpDomain(\"\");\n            setSearchQuery(\"\");\n        }\n    };\n    const handleDeleteSkill = (skillIdToDelete)=>{\n        if (!profile || !profile.skills) return;\n        const updatedSkills = profile.skills.filter((skill)=>{\n            const skillId = typeof skill === \"string\" ? skill : skill.skillId || skill._id || skill.id || skill.name;\n            return skillId !== skillIdToDelete;\n        });\n        setProfile({\n            ...profile,\n            skills: updatedSkills\n        });\n        setEditingProfileData({\n            ...editingProfileData,\n            skills: updatedSkills\n        });\n    };\n    const handleDeleteDomain = (domainIdToDelete)=>{\n        if (!profile || !profile.domains) return;\n        const updatedDomains = profile.domains.filter((domain)=>{\n            const domainId = typeof domain === \"string\" ? domain : domain.domainId || domain._id || domain.id || domain.name;\n            return domainId !== domainIdToDelete;\n        });\n        setProfile({\n            ...profile,\n            domains: updatedDomains\n        });\n        setEditingProfileData({\n            ...editingProfileData,\n            domains: updatedDomains\n        });\n    };\n    const handleRemoveProject = async (projectId)=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            const updatedProjects = (profile.projects || []).filter((project)=>project._id !== projectId);\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI({\n                ...profile,\n                projects: updatedProjects\n            });\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Project removed from profile successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error removing project:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to remove project from profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleRemoveExperience = async (experienceId)=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            const updatedExperiences = (profile.experiences || []).filter((experience)=>experience._id !== experienceId);\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI({\n                ...profile,\n                experiences: updatedExperiences\n            });\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Experience removed from profile successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error removing experience:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to remove experience from profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleProjectClick = (project)=>{\n        setSelectedProject(project);\n        setIsProjectDetailsOpen(true);\n    };\n    const handleCloseProjectDetails = ()=>{\n        setIsProjectDetailsOpen(false);\n        setSelectedProject(null);\n    };\n    const handleDeleteProfile = async ()=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.delete(\"/freelancer/profile/\".concat(profile._id));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            router.push(\"/freelancer/settings/profiles\");\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n        }\n    };\n    if (isLoading || !skillsAndDomainsLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                    menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                    active: \"Profiles\",\n                    isKycCheck: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                            menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                            activeMenu: \"Profiles\",\n                            breadcrumbItems: [\n                                {\n                                    label: \"Freelancer\",\n                                    link: \"/dashboard/freelancer\"\n                                },\n                                {\n                                    label: \"Settings\",\n                                    link: \"#\"\n                                },\n                                {\n                                    label: \"Profiles\",\n                                    link: \"/freelancer/settings/profiles\"\n                                },\n                                {\n                                    label: \"Profile Details\",\n                                    link: \"#\"\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading profile...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 560,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n            lineNumber: 553,\n            columnNumber: 7\n        }, this);\n    }\n    if (!profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                    menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                    active: \"Profiles\",\n                    isKycCheck: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 585,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                            menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                            activeMenu: \"Profiles\",\n                            breadcrumbItems: [\n                                {\n                                    label: \"Freelancer\",\n                                    link: \"/dashboard/freelancer\"\n                                },\n                                {\n                                    label: \"Settings\",\n                                    link: \"#\"\n                                },\n                                {\n                                    label: \"Profiles\",\n                                    link: \"/freelancer/settings/profiles\"\n                                },\n                                {\n                                    label: \"Profile Details\",\n                                    link: \"#\"\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Profile not found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: ()=>router.push(\"/freelancer/settings/profiles\"),\n                                        className: \"mt-4\",\n                                        children: \"Back to Profiles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 591,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n            lineNumber: 584,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 621,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"/freelancer/settings/profiles\"\n                            },\n                            {\n                                label: profile.profileName,\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/freelancer/settings/profiles\"),\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Profiles\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: profile.profileName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Edit your professional profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    onClick: handleUpdateProfile,\n                                                    disabled: isUpdating,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isUpdating ? \"Updating...\" : \"Save Changes\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    variant: \"destructive\",\n                                                    onClick: ()=>setDeleteDialogOpen(true),\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Delete\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Profile Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                            htmlFor: \"profileName\",\n                                                                            children: \"Profile Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm \".concat((editingProfileData.profileName || \"\").length === 0 ? \"text-red-500\" : (editingProfileData.profileName || \"\").length > 100 ? \"text-red-500\" : \"text-muted-foreground\"),\n                                                                            children: [\n                                                                                (editingProfileData.profileName || \"\").length,\n                                                                                \"/100\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 693,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"profileName\",\n                                                                    value: editingProfileData.profileName || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"profileName\", e.target.value),\n                                                                    placeholder: \"e.g., Frontend Developer\",\n                                                                    className: (editingProfileData.profileName || \"\").length === 0 || (editingProfileData.profileName || \"\").length > 100 ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"hourlyRate\",\n                                                                    children: \"Hourly Rate ($)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"hourlyRate\",\n                                                                    type: \"number\",\n                                                                    value: editingProfileData.hourlyRate || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"hourlyRate\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 723,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"description\",\n                                                                    children: \"Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat((editingProfileData.description || \"\").length < 10 ? \"text-red-500\" : (editingProfileData.description || \"\").length > 500 ? \"text-red-500\" : \"text-muted-foreground\"),\n                                                                    children: [\n                                                                        (editingProfileData.description || \"\").length,\n                                                                        \"/500 (min: 10)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 742,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                            id: \"description\",\n                                                            value: editingProfileData.description || \"\",\n                                                            onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                            placeholder: \"Describe your expertise and experience... (minimum 10 characters)\",\n                                                            rows: 4,\n                                                            className: (editingProfileData.description || \"\").length < 10 || (editingProfileData.description || \"\").length > 500 ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    children: \"Skills\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                            onValueChange: (value)=>{\n                                                                                setTmpSkill(value);\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            value: tmpSkill || \"\",\n                                                                            onOpenChange: (open)=>{\n                                                                                if (!open) setSearchQuery(\"\");\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                        placeholder: tmpSkill ? tmpSkill : \"Select skill\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                        lineNumber: 790,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 789,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"text\",\n                                                                                                    value: searchQuery,\n                                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                                    placeholder: \"Search skills\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 797,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 805,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 796,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        skillsOptions && skillsOptions.length > 0 && skillsOptions.filter((skill)=>{\n                                                                                            var _profile_skills;\n                                                                                            const skillName = skill.name || skill.label || skill.skillName;\n                                                                                            if (!skillName) return false;\n                                                                                            const matchesSearch = skillName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((s)=>{\n                                                                                                const existingSkillId = typeof s === \"string\" ? s : s.skillId || s._id || s.id || s.name;\n                                                                                                const currentSkillId = skill.skillId || skill._id || skill.id || skill.name;\n                                                                                                return existingSkillId === currentSkillId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                value: skill.name || skill.label || skill.skillName,\n                                                                                                children: skill.name || skill.label || skill.skillName\n                                                                                            }, skill.skillId || skill._id || skill.id || index, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                lineNumber: 844,\n                                                                                                columnNumber: 33\n                                                                                            }, this)),\n                                                                                        skillsOptions && skillsOptions.length > 0 && skillsOptions.filter((skill)=>{\n                                                                                            var _profile_skills;\n                                                                                            const skillName = skill.name || skill.label || skill.skillName;\n                                                                                            if (!skillName) return false;\n                                                                                            const matchesSearch = skillName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((s)=>{\n                                                                                                const existingSkillId = typeof s === \"string\" ? s : s.skillId || s._id || s.id || s.name;\n                                                                                                const currentSkillId = skill.skillId || skill._id || skill.id || skill.name;\n                                                                                                return existingSkillId === currentSkillId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                                            children: \"No matching skills\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 887,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 794,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 779,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                            variant: \"outline\",\n                                                                            type: \"button\",\n                                                                            size: \"icon\",\n                                                                            className: \"ml-2\",\n                                                                            disabled: !tmpSkill,\n                                                                            onClick: ()=>{\n                                                                                handleAddSkill();\n                                                                                setTmpSkill(\"\");\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 905,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 893,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 778,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                                    children: profile.skills && profile.skills.length > 0 ? profile.skills.map((skill, index)=>{\n                                                                        const skillId = typeof skill === \"string\" ? skill : skill.skillId || skill._id || skill.id || skill.name;\n                                                                        const skillName = getSkillNameById(skillId);\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                                            children: [\n                                                                                skillName,\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"ml-2 h-3 w-3 cursor-pointer\",\n                                                                                    onClick: ()=>handleDeleteSkill(skillId)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 926,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 921,\n                                                                            columnNumber: 29\n                                                                        }, this);\n                                                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm\",\n                                                                        children: \"No skills selected\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 934,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 908,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    children: \"Domains\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                            onValueChange: (value)=>{\n                                                                                setTmpDomain(value);\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            value: tmpDomain || \"\",\n                                                                            onOpenChange: (open)=>{\n                                                                                if (!open) setSearchQuery(\"\");\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                        placeholder: tmpDomain ? tmpDomain : \"Select domain\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                        lineNumber: 954,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 953,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"text\",\n                                                                                                    value: searchQuery,\n                                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                                    placeholder: \"Search domains\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 963,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 971,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 962,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        domainsOptions && domainsOptions.length > 0 && domainsOptions.filter((domain)=>{\n                                                                                            var _profile_domains;\n                                                                                            const domainName = domain.name || domain.label || domain.domainName;\n                                                                                            if (!domainName) return false;\n                                                                                            const matchesSearch = domainName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((d)=>{\n                                                                                                const existingDomainId = typeof d === \"string\" ? d : d.domainId || d._id || d.id || d.name;\n                                                                                                const currentDomainId = domain.domainId || domain._id || domain.id || domain.name;\n                                                                                                return existingDomainId === currentDomainId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).map((domain, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                value: domain.name || domain.label || domain.domainName,\n                                                                                                children: domain.name || domain.label || domain.domainName\n                                                                                            }, domain.domainId || domain._id || domain.id || index, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                lineNumber: 1011,\n                                                                                                columnNumber: 33\n                                                                                            }, this)),\n                                                                                        domainsOptions && domainsOptions.length > 0 && domainsOptions.filter((domain)=>{\n                                                                                            var _profile_domains;\n                                                                                            const domainName = domain.name || domain.label || domain.domainName;\n                                                                                            if (!domainName) return false;\n                                                                                            const matchesSearch = domainName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((d)=>{\n                                                                                                const existingDomainId = typeof d === \"string\" ? d : d.domainId || d._id || d.id || d.name;\n                                                                                                const currentDomainId = domain.domainId || domain._id || domain.id || domain.name;\n                                                                                                return existingDomainId === currentDomainId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                                            children: \"No matching domains\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 1060,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 960,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 943,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                            variant: \"outline\",\n                                                                            type: \"button\",\n                                                                            size: \"icon\",\n                                                                            className: \"ml-2\",\n                                                                            disabled: !tmpDomain,\n                                                                            onClick: ()=>{\n                                                                                handleAddDomain();\n                                                                                setTmpDomain(\"\");\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 1078,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1066,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 942,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                                    children: profile.domains && profile.domains.length > 0 ? profile.domains.map((domain, index)=>{\n                                                                        const domainId = typeof domain === \"string\" ? domain : domain.domainId || domain._id || domain.id || domain.name;\n                                                                        const domainName = getDomainNameById(domainId);\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                                            children: [\n                                                                                domainName,\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"ml-2 h-3 w-3 cursor-pointer\",\n                                                                                    onClick: ()=>handleDeleteDomain(domainId)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1099,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1094,\n                                                                            columnNumber: 29\n                                                                        }, this);\n                                                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm\",\n                                                                        children: \"No domains selected\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 1107,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1081,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1115,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"githubLink\",\n                                                                    children: \"GitHub Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1120,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"githubLink\",\n                                                                    value: editingProfileData.githubLink || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"githubLink\", e.target.value),\n                                                                    placeholder: \"https://github.com/username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1121,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1119,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"linkedinLink\",\n                                                                    children: \"LinkedIn Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1131,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"linkedinLink\",\n                                                                    value: editingProfileData.linkedinLink || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"linkedinLink\", e.target.value),\n                                                                    placeholder: \"https://linkedin.com/in/username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1132,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1130,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"personalWebsite\",\n                                                                    children: \"Personal Website\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1145,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"personalWebsite\",\n                                                                    value: editingProfileData.personalWebsite || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"personalWebsite\", e.target.value),\n                                                                    placeholder: \"https://yourwebsite.com\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1146,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1144,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"availability\",\n                                                                    children: \"Availability\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1156,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                    value: editingProfileData.availability || \"FREELANCE\",\n                                                                    onValueChange: (value)=>handleInputChange(\"availability\", value),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                placeholder: \"Select availability\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 1164,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1163,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"FULL_TIME\",\n                                                                                    children: \"Full Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1167,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"PART_TIME\",\n                                                                                    children: \"Part Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1168,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"CONTRACT\",\n                                                                                    children: \"Contract\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1169,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"FREELANCE\",\n                                                                                    children: \"Freelance\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1170,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1166,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1157,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1155,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1143,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                        className: \"text-xl font-semibold\",\n                                                        children: \"Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowProjectDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1190,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Projects\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1181,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            children: profile.projects && profile.projects.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4\",\n                                                children: profile.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cards_freelancerProjectCard__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                ...project,\n                                                                onClick: ()=>handleProjectClick(project)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1200,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleRemoveProject(project._id);\n                                                                },\n                                                                className: \"absolute top-2 right-2 z-10 h-8 w-8 p-0 bg-red-500/80 hover:bg-red-600/90 text-white rounded-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1214,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1205,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, project._id || index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1199,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1197,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                className: \"flex flex-col items-center justify-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-4\",\n                                                        children: \"No projects added to this profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1221,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowProjectDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1229,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Add Projects\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1224,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1220,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1195,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                        className: \"text-xl font-semibold\",\n                                                        children: \"Experience\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowExperienceDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1249,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Experience\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1240,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            children: profile.experiences && profile.experiences.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: profile.experiences.map((experience, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                        className: \"p-4 bg-background border\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-lg mb-1\",\n                                                                            children: experience.jobTitle || experience.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1265,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground mb-2\",\n                                                                            children: experience.company\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1268,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mb-3\",\n                                                                            children: [\n                                                                                new Date(experience.workFrom).toLocaleDateString(\"en-US\", {\n                                                                                    year: \"numeric\",\n                                                                                    month: \"short\"\n                                                                                }),\n                                                                                \" \",\n                                                                                \"-\",\n                                                                                \" \",\n                                                                                new Date(experience.workTo).toLocaleDateString(\"en-US\", {\n                                                                                    year: \"numeric\",\n                                                                                    month: \"short\"\n                                                                                })\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1271,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        experience.workDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-foreground\",\n                                                                            children: experience.workDescription\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1288,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1264,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRemoveExperience(experience._id),\n                                                                    className: \"text-destructive hover:text-destructive/80 ml-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 1301,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1293,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1263,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, experience._id || index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1259,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1256,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                className: \"flex flex-col items-center justify-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-4\",\n                                                        children: \"No experience added to this profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowExperienceDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1318,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Add Experience\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1313,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1309,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 627,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_ProjectSelectionDialog__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                open: showProjectDialog,\n                onOpenChange: setShowProjectDialog,\n                freelancerId: user.uid,\n                currentProfileId: profileId,\n                onSuccess: ()=>{\n                    fetchProfile();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1330,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_ExperienceSelectionDialog__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                open: showExperienceDialog,\n                onOpenChange: setShowExperienceDialog,\n                freelancerId: user.uid,\n                currentProfileId: profileId,\n                onSuccess: ()=>{\n                    fetchProfile();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1340,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1355,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1353,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: handleDeleteProfile,\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1367,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1360,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 1352,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1351,\n                columnNumber: 7\n            }, this),\n            selectedProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                open: isProjectDetailsOpen,\n                onOpenChange: handleCloseProjectDetails,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                    className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                className: \"text-2xl font-bold\",\n                                children: selectedProject.projectName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 1382,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1381,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                selectedProject.thumbnail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: selectedProject.thumbnail,\n                                        alt: \"\".concat(selectedProject.projectName, \" thumbnail\"),\n                                        className: \"w-full h-64 object-cover rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 1391,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1390,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1403,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: selectedProject.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1404,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1402,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1410,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: selectedProject.role\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1411,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1409,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Project Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1417,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: selectedProject.projectType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1418,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1416,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1401,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Duration\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1426,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: [\n                                                                new Date(selectedProject.start).toLocaleDateString(),\n                                                                \" -\",\n                                                                \" \",\n                                                                new Date(selectedProject.end).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1427,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1425,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Technologies Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1434,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: (_selectedProject_techUsed = selectedProject.techUsed) === null || _selectedProject_techUsed === void 0 ? void 0 : _selectedProject_techUsed.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    children: tech\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1440,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1437,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1433,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Links\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1449,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                selectedProject.githubLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: selectedProject.githubLink,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800\",\n                                                                    children: \"GitHub Repository\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1452,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedProject.liveDemoLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: selectedProject.liveDemoLink,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800\",\n                                                                    children: \"Live Demo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1462,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1450,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1448,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1424,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1400,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1387,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 1380,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1376,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n        lineNumber: 620,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfileDetailPage, \"xfNSYa67OHOkItQOXA1jvbqgfpY=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_20__.useSelector,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = ProfileDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProfileDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/[profileId]/page.tsx\n"));

/***/ })

});