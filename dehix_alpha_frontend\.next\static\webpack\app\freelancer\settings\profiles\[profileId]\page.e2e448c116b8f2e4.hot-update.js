"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/[profileId]/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/[profileId]/page.tsx":
/*!*******************************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/[profileId]/page.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfileDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_cards_freelancerProjectCard__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/cards/freelancerProjectCard */ \"(app-pages-browser)/./src/components/cards/freelancerProjectCard.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_dialogs_ProjectSelectionDialog__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/dialogs/ProjectSelectionDialog */ \"(app-pages-browser)/./src/components/dialogs/ProjectSelectionDialog.tsx\");\n/* harmony import */ var _components_dialogs_ExperienceSelectionDialog__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/dialogs/ExperienceSelectionDialog */ \"(app-pages-browser)/./src/components/dialogs/ExperienceSelectionDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProfileDetailPage() {\n    var _selectedProject_techUsed;\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_20__.useSelector)((state)=>state.user);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const profileId = params.profileId;\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProfileData, setEditingProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [skillsOptions, setSkillsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [domainsOptions, setDomainsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [skillsAndDomainsLoaded, setSkillsAndDomainsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProjectDialog, setShowProjectDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExperienceDialog, setShowExperienceDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [freelancerProjects, setFreelancerProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProjectDetailsOpen, setIsProjectDetailsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tmpSkill, setTmpSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tmpDomain, setTmpDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (profileId) {\n            fetchSkillsAndDomains().then(()=>{\n                fetchProfile();\n                fetchFreelancerProjects();\n            });\n        }\n    }, [\n        profileId,\n        user.uid\n    ]);\n    // Helper function to get skill name from ID\n    const getSkillNameById = (skillId)=>{\n        if (!skillId || !skillsOptions || skillsOptions.length === 0) {\n            return skillId || \"\";\n        }\n        // Try multiple matching strategies\n        let skill = skillsOptions.find((s)=>s.skillId === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s._id === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s.id === skillId);\n        if (!skill) skill = skillsOptions.find((s)=>s.name === skillId);\n        // If still not found, try case-insensitive name matching\n        if (!skill) {\n            skill = skillsOptions.find((s)=>{\n                var _this;\n                return ((_this = s.name || s.label || s.skillName) === null || _this === void 0 ? void 0 : _this.toLowerCase()) === skillId.toLowerCase();\n            });\n        }\n        return skill ? skill.name || skill.label || skill.skillName || skillId : skillId;\n    };\n    // Helper function to get domain name from ID\n    const getDomainNameById = (domainId)=>{\n        if (!domainId || !domainsOptions || domainsOptions.length === 0) {\n            return domainId || \"\";\n        }\n        // Try multiple matching strategies\n        let domain = domainsOptions.find((d)=>d.domainId === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d._id === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d.id === domainId);\n        if (!domain) domain = domainsOptions.find((d)=>d.name === domainId);\n        // If still not found, try case-insensitive name matching\n        if (!domain) {\n            domain = domainsOptions.find((d)=>{\n                var _this;\n                return ((_this = d.name || d.label || d.domainName) === null || _this === void 0 ? void 0 : _this.toLowerCase()) === domainId.toLowerCase();\n            });\n        }\n        return domain ? domain.name || domain.label || domain.domainName || domainId : domainId;\n    };\n    // Helper function to transform profile data for backend API\n    const transformProfileForAPI = (profileData)=>{\n        var _profileData_skills, _profileData_domains;\n        const transformedSkills = ((_profileData_skills = profileData.skills) === null || _profileData_skills === void 0 ? void 0 : _profileData_skills.map((skill)=>{\n            if (typeof skill === \"string\") {\n                return skill;\n            }\n            // Prioritize skillId field, then fallback to other ID fields\n            const skillId = skill.skillId || skill._id || skill.id || skill.value || skill.name;\n            return skillId;\n        })) || [];\n        const transformedDomains = ((_profileData_domains = profileData.domains) === null || _profileData_domains === void 0 ? void 0 : _profileData_domains.map((domain)=>{\n            if (typeof domain === \"string\") {\n                return domain;\n            }\n            // Prioritize domainId field, then fallback to other ID fields\n            const domainId = domain.domainId || domain._id || domain.id || domain.value || domain.name;\n            return domainId;\n        })) || [];\n        return {\n            ...profileData,\n            skills: transformedSkills,\n            domains: transformedDomains\n        };\n    };\n    const fetchProfile = async ()=>{\n        if (!profileId) return;\n        setIsLoading(true);\n        try {\n            var _profileData_skills, _profileData_domains;\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/profile/\".concat(profileId));\n            const profileData = response.data.data;\n            // If profile has projects, fetch complete project data to ensure we have thumbnails\n            if (profileData.projects && profileData.projects.length > 0) {\n                try {\n                    const freelancerResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid));\n                    const freelancerData = freelancerResponse.data.data || {};\n                    const freelancerProjects = freelancerData.projects || {};\n                    // Convert projects object to array if it's an object\n                    const allFreelancerProjects = Array.isArray(freelancerProjects) ? freelancerProjects : Object.values(freelancerProjects);\n                    // Merge profile projects with complete freelancer project data\n                    const enrichedProjects = profileData.projects.map((profileProject)=>{\n                        const fullProject = allFreelancerProjects.find((fp)=>fp._id === profileProject._id);\n                        // Use full project data if available, otherwise use profile project data\n                        return fullProject || profileProject;\n                    });\n                    profileData.projects = enrichedProjects;\n                } catch (projectError) {\n                    console.warn(\"Could not fetch complete project data:\", projectError);\n                // Continue with existing profile data if project fetch fails\n                }\n            }\n            // Debug: Log the raw profile data to understand the structure\n            console.log(\"=== PROFILE DEBUG ===\");\n            console.log(\"Raw profile data from API:\", profileData);\n            console.log(\"Profile skills:\", profileData.skills);\n            console.log(\"Profile domains:\", profileData.domains);\n            console.log(\"Skills type:\", typeof ((_profileData_skills = profileData.skills) === null || _profileData_skills === void 0 ? void 0 : _profileData_skills[0]));\n            console.log(\"Domains type:\", typeof ((_profileData_domains = profileData.domains) === null || _profileData_domains === void 0 ? void 0 : _profileData_domains[0]));\n            console.log(\"=== END DEBUG ===\");\n            // Ensure skills and domains are properly formatted as arrays\n            const processedProfileData = {\n                ...profileData,\n                skills: Array.isArray(profileData.skills) ? profileData.skills : [],\n                domains: Array.isArray(profileData.domains) ? profileData.domains : []\n            };\n            setProfile(processedProfileData);\n            setEditingProfileData(processedProfileData);\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profile\",\n                variant: \"destructive\"\n            });\n            router.push(\"/freelancer/settings/profiles\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const fetchSkillsAndDomains = async ()=>{\n        try {\n            // Fetch both complete collections and freelancer data\n            const [skillsResponse, domainsResponse, freelancerResponse] = await Promise.all([\n                _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/skills\"),\n                _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/domain\"),\n                _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid))\n            ]);\n            // Complete skills and domains for ID resolution\n            const allSkills = skillsResponse.data.data || [];\n            const allDomains = domainsResponse.data.data || [];\n            console.log(\"=== SKILLS & DOMAINS DEBUG ===\");\n            console.log(\"All skills from /skills:\", allSkills);\n            console.log(\"All domains from /domain:\", allDomains);\n            // Freelancer's personal skills and domains for dropdown\n            const freelancerData = freelancerResponse.data.data || {};\n            const freelancerSkills = freelancerData.skills || [];\n            const freelancerDomains = freelancerData.domain || [];\n            console.log(\"Freelancer skills:\", freelancerSkills);\n            console.log(\"Freelancer domains:\", freelancerDomains);\n            console.log(\"=== END SKILLS & DOMAINS DEBUG ===\");\n            // Set all data\n            setSkillsOptions(allSkills);\n            setDomainsOptions(allDomains);\n            setFreelancerSkillsOptions(freelancerSkills);\n            setFreelancerDomainsOptions(freelancerDomains);\n            setSkillsAndDomainsLoaded(true);\n        } catch (error) {\n            console.error(\"Error fetching skills and domains:\", error);\n        }\n    };\n    const fetchFreelancerProjects = async ()=>{\n        try {\n            const freelancerResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.get(\"/freelancer/\".concat(user.uid));\n            const freelancerData = freelancerResponse.data.data || {};\n            const projectsData = freelancerData.projects || {};\n            setFreelancerProjects(projectsData);\n        } catch (error) {\n            console.error(\"Error fetching freelancer projects:\", error);\n        }\n    };\n    const handleUpdateProfile = async ()=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        // Client-side validation\n        if (!editingProfileData.profileName || editingProfileData.profileName.trim().length === 0) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Profile name is required\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!editingProfileData.description || editingProfileData.description.trim().length < 10) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Description must be at least 10 characters long\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (editingProfileData.profileName.length > 100) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Profile name must be less than 100 characters\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (editingProfileData.description.length > 500) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Validation Error\",\n                description: \"Description must be less than 500 characters\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsUpdating(true);\n        try {\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI(editingProfileData);\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Profile updated successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setEditingProfileData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleAddSkill = ()=>{\n        if (!tmpSkill || !profile || !skillsOptions || skillsOptions.length === 0) return;\n        const selectedSkill = skillsOptions.find((skill)=>(skill.name || skill.label || skill.skillName) === tmpSkill);\n        if (selectedSkill) {\n            var _profile_skills;\n            // Add the skillId (string) to the profile, not the entire object\n            const skillIdToAdd = selectedSkill.skillId || selectedSkill._id || selectedSkill.id || selectedSkill.name;\n            // Check if skill is already added by comparing IDs\n            const isAlreadyAdded = (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((skill)=>{\n                const existingSkillId = typeof skill === \"string\" ? skill : skill.skillId || skill._id || skill.id || skill.name;\n                return existingSkillId === skillIdToAdd;\n            });\n            if (!isAlreadyAdded) {\n                const updatedSkills = [\n                    ...profile.skills || [],\n                    skillIdToAdd\n                ];\n                setProfile({\n                    ...profile,\n                    skills: updatedSkills\n                });\n                setEditingProfileData({\n                    ...editingProfileData,\n                    skills: updatedSkills\n                });\n            }\n            setTmpSkill(\"\");\n            setSearchQuery(\"\");\n        }\n    };\n    const handleAddDomain = ()=>{\n        if (!tmpDomain || !profile || !domainsOptions || domainsOptions.length === 0) return;\n        const selectedDomain = domainsOptions.find((domain)=>(domain.name || domain.label || domain.domainName) === tmpDomain);\n        if (selectedDomain) {\n            var _profile_domains;\n            // Add the domainId (string) to the profile, not the entire object\n            const domainIdToAdd = selectedDomain.domainId || selectedDomain._id || selectedDomain.id || selectedDomain.name;\n            // Check if domain is already added by comparing IDs\n            const isAlreadyAdded = (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((domain)=>{\n                const existingDomainId = typeof domain === \"string\" ? domain : domain.domainId || domain._id || domain.id || domain.name;\n                return existingDomainId === domainIdToAdd;\n            });\n            if (!isAlreadyAdded) {\n                const updatedDomains = [\n                    ...profile.domains || [],\n                    domainIdToAdd\n                ];\n                setProfile({\n                    ...profile,\n                    domains: updatedDomains\n                });\n                setEditingProfileData({\n                    ...editingProfileData,\n                    domains: updatedDomains\n                });\n            }\n            setTmpDomain(\"\");\n            setSearchQuery(\"\");\n        }\n    };\n    const handleDeleteSkill = (skillIdToDelete)=>{\n        if (!profile || !profile.skills) return;\n        const updatedSkills = profile.skills.filter((skill)=>{\n            const skillId = typeof skill === \"string\" ? skill : skill.skillId || skill._id || skill.id || skill.name;\n            return skillId !== skillIdToDelete;\n        });\n        setProfile({\n            ...profile,\n            skills: updatedSkills\n        });\n        setEditingProfileData({\n            ...editingProfileData,\n            skills: updatedSkills\n        });\n    };\n    const handleDeleteDomain = (domainIdToDelete)=>{\n        if (!profile || !profile.domains) return;\n        const updatedDomains = profile.domains.filter((domain)=>{\n            const domainId = typeof domain === \"string\" ? domain : domain.domainId || domain._id || domain.id || domain.name;\n            return domainId !== domainIdToDelete;\n        });\n        setProfile({\n            ...profile,\n            domains: updatedDomains\n        });\n        setEditingProfileData({\n            ...editingProfileData,\n            domains: updatedDomains\n        });\n    };\n    const handleRemoveProject = async (projectId)=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            const updatedProjects = (profile.projects || []).filter((project)=>project._id !== projectId);\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI({\n                ...profile,\n                projects: updatedProjects\n            });\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Project removed from profile successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error removing project:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to remove project from profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleRemoveExperience = async (experienceId)=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            const updatedExperiences = (profile.experiences || []).filter((experience)=>experience._id !== experienceId);\n            // Transform the data to match backend schema\n            const updatePayload = transformProfileForAPI({\n                ...profile,\n                experiences: updatedExperiences\n            });\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.put(\"/freelancer/profile/\".concat(profile._id), updatePayload);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Experience removed from profile successfully\"\n            });\n            fetchProfile();\n        } catch (error) {\n            console.error(\"Error removing experience:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to remove experience from profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleProjectClick = (project)=>{\n        setSelectedProject(project);\n        setIsProjectDetailsOpen(true);\n    };\n    const handleCloseProjectDetails = ()=>{\n        setIsProjectDetailsOpen(false);\n        setSelectedProject(null);\n    };\n    const handleDeleteProfile = async ()=>{\n        if (!(profile === null || profile === void 0 ? void 0 : profile._id)) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_6__.axiosInstance.delete(\"/freelancer/profile/\".concat(profile._id));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            router.push(\"/freelancer/settings/profiles\");\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n        }\n    };\n    if (isLoading || !skillsAndDomainsLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                    menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                    active: \"Profiles\",\n                    isKycCheck: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 581,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                            menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                            activeMenu: \"Profiles\",\n                            breadcrumbItems: [\n                                {\n                                    label: \"Freelancer\",\n                                    link: \"/dashboard/freelancer\"\n                                },\n                                {\n                                    label: \"Settings\",\n                                    link: \"#\"\n                                },\n                                {\n                                    label: \"Profiles\",\n                                    link: \"/freelancer/settings/profiles\"\n                                },\n                                {\n                                    label: \"Profile Details\",\n                                    link: \"#\"\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading profile...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n            lineNumber: 580,\n            columnNumber: 7\n        }, this);\n    }\n    if (!profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                    menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                    active: \"Profiles\",\n                    isKycCheck: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 612,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                            menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                            activeMenu: \"Profiles\",\n                            breadcrumbItems: [\n                                {\n                                    label: \"Freelancer\",\n                                    link: \"/dashboard/freelancer\"\n                                },\n                                {\n                                    label: \"Settings\",\n                                    link: \"#\"\n                                },\n                                {\n                                    label: \"Profiles\",\n                                    link: \"/freelancer/settings/profiles\"\n                                },\n                                {\n                                    label: \"Profile Details\",\n                                    link: \"#\"\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Profile not found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: ()=>router.push(\"/freelancer/settings/profiles\"),\n                                        className: \"mt-4\",\n                                        children: \"Back to Profiles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n            lineNumber: 611,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 648,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_4__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"/freelancer/settings/profiles\"\n                            },\n                            {\n                                label: profile.profileName,\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/freelancer/settings/profiles\"),\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Profiles\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: profile.profileName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Edit your professional profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    onClick: handleUpdateProfile,\n                                                    disabled: isUpdating,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isUpdating ? \"Updating...\" : \"Save Changes\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    variant: \"destructive\",\n                                                    onClick: ()=>setDeleteDialogOpen(true),\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Delete\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Profile Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                            htmlFor: \"profileName\",\n                                                                            children: \"Profile Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 719,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm \".concat((editingProfileData.profileName || \"\").length === 0 ? \"text-red-500\" : (editingProfileData.profileName || \"\").length > 100 ? \"text-red-500\" : \"text-muted-foreground\"),\n                                                                            children: [\n                                                                                (editingProfileData.profileName || \"\").length,\n                                                                                \"/100\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 720,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"profileName\",\n                                                                    value: editingProfileData.profileName || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"profileName\", e.target.value),\n                                                                    placeholder: \"e.g., Frontend Developer\",\n                                                                    className: (editingProfileData.profileName || \"\").length === 0 || (editingProfileData.profileName || \"\").length > 100 ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 717,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"hourlyRate\",\n                                                                    children: \"Hourly Rate ($)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 749,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"hourlyRate\",\n                                                                    type: \"number\",\n                                                                    value: editingProfileData.hourlyRate || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"hourlyRate\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 750,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"description\",\n                                                                    children: \"Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat((editingProfileData.description || \"\").length < 10 ? \"text-red-500\" : (editingProfileData.description || \"\").length > 500 ? \"text-red-500\" : \"text-muted-foreground\"),\n                                                                    children: [\n                                                                        (editingProfileData.description || \"\").length,\n                                                                        \"/500 (min: 10)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                            id: \"description\",\n                                                            value: editingProfileData.description || \"\",\n                                                            onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                            placeholder: \"Describe your expertise and experience... (minimum 10 characters)\",\n                                                            rows: 4,\n                                                            className: (editingProfileData.description || \"\").length < 10 || (editingProfileData.description || \"\").length > 500 ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 782,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    children: \"Skills\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                            onValueChange: (value)=>{\n                                                                                setTmpSkill(value);\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            value: tmpSkill || \"\",\n                                                                            onOpenChange: (open)=>{\n                                                                                if (!open) setSearchQuery(\"\");\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                        placeholder: tmpSkill ? tmpSkill : \"Select skill\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                        lineNumber: 817,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 816,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"text\",\n                                                                                                    value: searchQuery,\n                                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                                    placeholder: \"Search skills\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 824,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 832,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 823,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        skillsOptions && skillsOptions.length > 0 && skillsOptions.filter((skill)=>{\n                                                                                            var _profile_skills;\n                                                                                            const skillName = skill.name || skill.label || skill.skillName;\n                                                                                            if (!skillName) return false;\n                                                                                            const matchesSearch = skillName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((s)=>{\n                                                                                                const existingSkillId = typeof s === \"string\" ? s : s.skillId || s._id || s.id || s.name;\n                                                                                                const currentSkillId = skill.skillId || skill._id || skill.id || skill.name;\n                                                                                                return existingSkillId === currentSkillId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                value: skill.name || skill.label || skill.skillName,\n                                                                                                children: skill.name || skill.label || skill.skillName\n                                                                                            }, skill.skillId || skill._id || skill.id || index, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                lineNumber: 871,\n                                                                                                columnNumber: 33\n                                                                                            }, this)),\n                                                                                        skillsOptions && skillsOptions.length > 0 && skillsOptions.filter((skill)=>{\n                                                                                            var _profile_skills;\n                                                                                            const skillName = skill.name || skill.label || skill.skillName;\n                                                                                            if (!skillName) return false;\n                                                                                            const matchesSearch = skillName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_skills = profile.skills) === null || _profile_skills === void 0 ? void 0 : _profile_skills.some((s)=>{\n                                                                                                const existingSkillId = typeof s === \"string\" ? s : s.skillId || s._id || s.id || s.name;\n                                                                                                const currentSkillId = skill.skillId || skill._id || skill.id || skill.name;\n                                                                                                return existingSkillId === currentSkillId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                                            children: \"No matching skills\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 914,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 821,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 806,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                            variant: \"outline\",\n                                                                            type: \"button\",\n                                                                            size: \"icon\",\n                                                                            className: \"ml-2\",\n                                                                            disabled: !tmpSkill,\n                                                                            onClick: ()=>{\n                                                                                handleAddSkill();\n                                                                                setTmpSkill(\"\");\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 932,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 920,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                                    children: profile.skills && profile.skills.length > 0 ? profile.skills.map((skill, index)=>{\n                                                                        // Handle both populated objects and ID strings\n                                                                        const skillId = typeof skill === \"string\" ? skill : skill._id;\n                                                                        const skillName = typeof skill === \"string\" ? getSkillNameById(skill) : skill.label || skill.name || skill._id;\n                                                                        console.log(\"Displaying skill:\", {\n                                                                            skill,\n                                                                            skillId,\n                                                                            skillName\n                                                                        });\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                                            children: [\n                                                                                skillName,\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"ml-2 h-3 w-3 cursor-pointer\",\n                                                                                    onClick: ()=>handleDeleteSkill(skillId)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 958,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 953,\n                                                                            columnNumber: 29\n                                                                        }, this);\n                                                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm\",\n                                                                        children: \"No skills selected\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 966,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 935,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    children: \"Domains\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 973,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                            onValueChange: (value)=>{\n                                                                                setTmpDomain(value);\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            value: tmpDomain || \"\",\n                                                                            onOpenChange: (open)=>{\n                                                                                if (!open) setSearchQuery(\"\");\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                        placeholder: tmpDomain ? tmpDomain : \"Select domain\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                        lineNumber: 986,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 985,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"text\",\n                                                                                                    value: searchQuery,\n                                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                                    placeholder: \"Search domains\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 995,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                    lineNumber: 1003,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 994,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        domainsOptions && domainsOptions.length > 0 && domainsOptions.filter((domain)=>{\n                                                                                            var _profile_domains;\n                                                                                            const domainName = domain.name || domain.label || domain.domainName;\n                                                                                            if (!domainName) return false;\n                                                                                            const matchesSearch = domainName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((d)=>{\n                                                                                                const existingDomainId = typeof d === \"string\" ? d : d.domainId || d._id || d.id || d.name;\n                                                                                                const currentDomainId = domain.domainId || domain._id || domain.id || domain.name;\n                                                                                                return existingDomainId === currentDomainId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).map((domain, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                value: domain.name || domain.label || domain.domainName,\n                                                                                                children: domain.name || domain.label || domain.domainName\n                                                                                            }, domain.domainId || domain._id || domain.id || index, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                                lineNumber: 1043,\n                                                                                                columnNumber: 33\n                                                                                            }, this)),\n                                                                                        domainsOptions && domainsOptions.length > 0 && domainsOptions.filter((domain)=>{\n                                                                                            var _profile_domains;\n                                                                                            const domainName = domain.name || domain.label || domain.domainName;\n                                                                                            if (!domainName) return false;\n                                                                                            const matchesSearch = domainName.toLowerCase().includes(searchQuery.toLowerCase());\n                                                                                            const isAlreadySelected = profile === null || profile === void 0 ? void 0 : (_profile_domains = profile.domains) === null || _profile_domains === void 0 ? void 0 : _profile_domains.some((d)=>{\n                                                                                                const existingDomainId = typeof d === \"string\" ? d : d.domainId || d._id || d.id || d.name;\n                                                                                                const currentDomainId = domain.domainId || domain._id || domain.id || domain.name;\n                                                                                                return existingDomainId === currentDomainId;\n                                                                                            });\n                                                                                            return matchesSearch && !isAlreadySelected;\n                                                                                        }).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                                            children: \"No matching domains\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                            lineNumber: 1092,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 992,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                            variant: \"outline\",\n                                                                            type: \"button\",\n                                                                            size: \"icon\",\n                                                                            className: \"ml-2\",\n                                                                            disabled: !tmpDomain,\n                                                                            onClick: ()=>{\n                                                                                handleAddDomain();\n                                                                                setTmpDomain(\"\");\n                                                                                setSearchQuery(\"\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 1110,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1098,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 974,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                                    children: profile.domains && profile.domains.length > 0 ? profile.domains.map((domain, index)=>{\n                                                                        // Handle both populated objects and ID strings\n                                                                        const domainId = typeof domain === \"string\" ? domain : domain._id;\n                                                                        const domainName = typeof domain === \"string\" ? getDomainNameById(domain) : domain.label || domain.name || domain._id;\n                                                                        console.log(\"Displaying domain:\", {\n                                                                            domain,\n                                                                            domainId,\n                                                                            domainName\n                                                                        });\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                                            children: [\n                                                                                domainName,\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"ml-2 h-3 w-3 cursor-pointer\",\n                                                                                    onClick: ()=>handleDeleteDomain(domainId)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1136,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1131,\n                                                                            columnNumber: 29\n                                                                        }, this);\n                                                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm\",\n                                                                        children: \"No domains selected\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 1144,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1113,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1152,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"githubLink\",\n                                                                    children: \"GitHub Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1157,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"githubLink\",\n                                                                    value: editingProfileData.githubLink || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"githubLink\", e.target.value),\n                                                                    placeholder: \"https://github.com/username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1158,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1156,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"linkedinLink\",\n                                                                    children: \"LinkedIn Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1168,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"linkedinLink\",\n                                                                    value: editingProfileData.linkedinLink || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"linkedinLink\", e.target.value),\n                                                                    placeholder: \"https://linkedin.com/in/username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1169,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1167,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"personalWebsite\",\n                                                                    children: \"Personal Website\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1182,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    id: \"personalWebsite\",\n                                                                    value: editingProfileData.personalWebsite || \"\",\n                                                                    onChange: (e)=>handleInputChange(\"personalWebsite\", e.target.value),\n                                                                    placeholder: \"https://yourwebsite.com\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1183,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1181,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                    htmlFor: \"availability\",\n                                                                    children: \"Availability\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1193,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                    value: editingProfileData.availability || \"FREELANCE\",\n                                                                    onValueChange: (value)=>handleInputChange(\"availability\", value),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                placeholder: \"Select availability\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                lineNumber: 1201,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1200,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"FULL_TIME\",\n                                                                                    children: \"Full Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1204,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"PART_TIME\",\n                                                                                    children: \"Part Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1205,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"CONTRACT\",\n                                                                                    children: \"Contract\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1206,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                    value: \"FREELANCE\",\n                                                                                    children: \"Freelance\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                                    lineNumber: 1207,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1203,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1194,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1192,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1180,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                        className: \"text-xl font-semibold\",\n                                                        children: \"Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowProjectDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1227,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Projects\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1222,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1218,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            children: profile.projects && profile.projects.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4\",\n                                                children: profile.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cards_freelancerProjectCard__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                ...project,\n                                                                onClick: ()=>handleProjectClick(project)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1237,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleRemoveProject(project._id);\n                                                                },\n                                                                className: \"absolute top-2 right-2 z-10 h-8 w-8 p-0 bg-red-500/80 hover:bg-red-600/90 text-white rounded-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1251,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1242,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, project._id || index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1236,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1234,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                className: \"flex flex-col items-center justify-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-4\",\n                                                        children: \"No projects added to this profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1258,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowProjectDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1266,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Add Projects\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1261,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1257,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1232,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                                                        className: \"text-xl font-semibold\",\n                                                        children: \"Experience\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowExperienceDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1286,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Experience\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1277,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.CardContent, {\n                                            children: profile.experiences && profile.experiences.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: profile.experiences.map((experience, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                        className: \"p-4 bg-background border\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-lg mb-1\",\n                                                                            children: experience.jobTitle || experience.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1302,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground mb-2\",\n                                                                            children: experience.company\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1305,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mb-3\",\n                                                                            children: [\n                                                                                new Date(experience.workFrom).toLocaleDateString(\"en-US\", {\n                                                                                    year: \"numeric\",\n                                                                                    month: \"short\"\n                                                                                }),\n                                                                                \" \",\n                                                                                \"-\",\n                                                                                \" \",\n                                                                                new Date(experience.workTo).toLocaleDateString(\"en-US\", {\n                                                                                    year: \"numeric\",\n                                                                                    month: \"short\"\n                                                                                })\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1308,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        experience.workDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-foreground\",\n                                                                            children: experience.workDescription\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                            lineNumber: 1325,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1301,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRemoveExperience(experience._id),\n                                                                    className: \"text-destructive hover:text-destructive/80 ml-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                        lineNumber: 1338,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1330,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1300,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, experience._id || index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1296,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1293,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                                className: \"flex flex-col items-center justify-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-4\",\n                                                        children: \"No experience added to this profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1347,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowExperienceDialog(true),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                lineNumber: 1355,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Add Experience\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                lineNumber: 1346,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1291,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 654,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_ProjectSelectionDialog__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                open: showProjectDialog,\n                onOpenChange: setShowProjectDialog,\n                freelancerId: user.uid,\n                currentProfileId: profileId,\n                onSuccess: ()=>{\n                    fetchProfile();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1367,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_ExperienceSelectionDialog__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                open: showExperienceDialog,\n                onOpenChange: setShowExperienceDialog,\n                freelancerId: user.uid,\n                currentProfileId: profileId,\n                onSuccess: ()=>{\n                    fetchProfile();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1392,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1390,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: handleDeleteProfile,\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1404,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1397,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 1389,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1388,\n                columnNumber: 7\n            }, this),\n            selectedProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                open: isProjectDetailsOpen,\n                onOpenChange: handleCloseProjectDetails,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                    className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                className: \"text-2xl font-bold\",\n                                children: selectedProject.projectName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                lineNumber: 1419,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1418,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                selectedProject.thumbnail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: selectedProject.thumbnail,\n                                        alt: \"\".concat(selectedProject.projectName, \" thumbnail\"),\n                                        className: \"w-full h-64 object-cover rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                        lineNumber: 1428,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1427,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1440,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: selectedProject.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1441,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1439,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1447,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: selectedProject.role\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1448,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1446,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Project Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1454,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: selectedProject.projectType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1455,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1453,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1438,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Duration\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1463,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: [\n                                                                new Date(selectedProject.start).toLocaleDateString(),\n                                                                \" -\",\n                                                                \" \",\n                                                                new Date(selectedProject.end).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1464,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1462,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Technologies Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1471,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: (_selectedProject_techUsed = selectedProject.techUsed) === null || _selectedProject_techUsed === void 0 ? void 0 : _selectedProject_techUsed.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    children: tech\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1477,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1474,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1470,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: \"Links\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1486,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                selectedProject.githubLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: selectedProject.githubLink,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800\",\n                                                                    children: \"GitHub Repository\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1489,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedProject.liveDemoLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: selectedProject.liveDemoLink,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800\",\n                                                                    children: \"Live Demo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                                    lineNumber: 1499,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                            lineNumber: 1487,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                                    lineNumber: 1485,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                            lineNumber: 1461,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                                    lineNumber: 1437,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                            lineNumber: 1424,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                    lineNumber: 1417,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n                lineNumber: 1413,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\[profileId]\\\\page.tsx\",\n        lineNumber: 647,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfileDetailPage, \"xfNSYa67OHOkItQOXA1jvbqgfpY=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_20__.useSelector,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = ProfileDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProfileDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/[profileId]/page.tsx\n"));

/***/ })

});